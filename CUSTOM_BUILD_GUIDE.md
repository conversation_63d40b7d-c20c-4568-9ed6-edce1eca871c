# Custom RouterOS Build Guide

This guide shows you how to create custom RouterOS ISO images for x86 and ARM64 with your own cryptographic keys and email address replacement.

## Overview

You want to:
1. Replace `<EMAIL>` with `<EMAIL>` 
2. Use your own cryptographic keys for licensing
3. Build custom x86 and ARM64 ISO images
4. Generate valid licenses for your custom build

## Quick Start

### 1. Setup Environment

```bash
# Make setup script executable and run it
chmod +x setup_custom_build.sh
./setup_custom_build.sh
```

This will:
- Install all required packages
- Generate your custom cryptographic keys
- Create build scripts
- Set up the environment

### 2. Build Custom RouterOS

```bash
# Build x86 ISO
./build_custom_routeros.sh 7.19.4 x86

# Build ARM64 ISO  
./build_custom_routeros.sh 7.19.4 arm64

# Or build both at once
./build_all.sh 7.19.4
```

### 3. Generate Licenses

```bash
# Generate a license for your custom RouterOS
./generate_license.sh CUST-OM01 7 4
```

## Detailed Process

### Environment Variables

The build process uses these key environment variables:

```bash
# Your custom keys (automatically generated)
CUSTOM_LICENSE_PRIVATE_KEY="your_license_private_key"
CUSTOM_LICENSE_PUBLIC_KEY="your_license_public_key"
CUSTOM_NPK_SIGN_PRIVATE_KEY="your_npk_private_key"
CUSTOM_NPK_SIGN_PUBLIC_KEY="your_npk_public_key"

# Original MikroTik keys (you need to find these)
MIKRO_LICENSE_PUBLIC_KEY="original_mikrotik_license_key"
MIKRO_NPK_SIGN_PUBLIC_KEY="original_mikrotik_npk_key"

# Custom URLs (replace with your servers)
CUSTOM_LICENCE_URL="your-license-server.com"
CUSTOM_UPGRADE_URL="your-upgrade-server.com"
CUSTOM_RENEW_URL="your-renew-server.com"
CUSTOM_CLOUD_URL="your-cloud-server.com"

# Email replacement
CUSTOM_EMAIL="<EMAIL>"
ORIGINAL_EMAIL="<EMAIL>"
```

### Build Process

#### 1. Download RouterOS
- Downloads official RouterOS ISO from MikroTik
- Supports both x86 and ARM64 architectures

#### 2. Extract and Patch
- Mounts ISO and extracts contents
- Patches system NPK package with your keys
- Patches kernel/bootloader binaries
- Replaces email addresses in all files
- Updates logo and branding

#### 3. Sign Packages
- Signs all NPK packages with your custom keys
- Ensures packages are trusted by your custom RouterOS

#### 4. Create Custom ISO
- Rebuilds ISO with patched components
- Creates bootable custom RouterOS image

### Key Replacement Process

The patching process replaces:

1. **License Public Keys**: Original MikroTik → Your custom keys
2. **NPK Signing Keys**: Original MikroTik → Your custom keys  
3. **URLs**: MikroTik servers → Your custom servers
4. **Email Addresses**: `<EMAIL>` → `<EMAIL>`
5. **Branding**: Updates logo and system information

### File Structure

```
MikroTikPatch-7.19.4/
├── setup_custom_build.sh      # Main setup script
├── custom_build_env.sh         # Environment variables
├── patch_custom.py             # Enhanced patch script
├── build_custom_routeros.sh    # Build script
├── build_all.sh                # Build all architectures
├── generate_license.sh         # License generator
├── mikrotik_keygen.py          # License key generator
├── patch.py                    # Original patch script
├── npk.py                      # NPK package tools
└── mikro.py                    # MikroTik crypto functions
```

## Advanced Configuration

### Custom Servers

Update `custom_build_env.sh` to point to your servers:

```bash
export CUSTOM_LICENCE_URL="license.yourdomain.com"
export CUSTOM_UPGRADE_URL="upgrade.yourdomain.com"
export CUSTOM_RENEW_URL="renew.yourdomain.com"
export CUSTOM_CLOUD_URL="cloud.yourdomain.com"
```

### Custom Branding

The build process automatically:
- Replaces email in `nova/lib/console/logo.txt`
- Updates system branding
- Replaces URLs throughout the system

### License Generation

Generate licenses for your custom RouterOS:

```bash
# Basic license
./generate_license.sh SOFTWARE-ID VERSION LEVEL

# Examples
./generate_license.sh CUST-OM01 7 4    # Standard license
./generate_license.sh CUST-OM02 7 6    # Unlimited license
./generate_license.sh TEST-0001 6 1    # Limited v6 license
```

## Requirements

### System Requirements
- Ubuntu 20.04+ or similar Linux distribution
- At least 4GB RAM
- 10GB free disk space
- sudo access

### Software Dependencies
- Python 3.8+
- cryptography library
- Standard Linux build tools
- ISO manipulation tools

### Original MikroTik Keys

You need to obtain the original MikroTik public keys:
- `MIKRO_LICENSE_PUBLIC_KEY`
- `MIKRO_NPK_SIGN_PUBLIC_KEY`
- `MIKRO_CLOUD_PUBLIC_KEY`

These are used to identify and replace the original keys in the RouterOS binaries.

## Security Considerations

⚠️ **Important Security Notes**:

1. **Key Management**: Keep your private keys secure and backed up
2. **Legal Compliance**: Ensure compliance with MikroTik's terms of service
3. **Educational Purpose**: This is for educational and research purposes
4. **Network Security**: Use proper network security when deploying custom builds

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   sudo chown -R $USER:$USER .
   ```

2. **Missing Dependencies**
   ```bash
   sudo apt-get update
   sudo apt-get install -y build-essential
   ```

3. **ISO Mount Errors**
   ```bash
   sudo umount ./iso 2>/dev/null || true
   sudo rm -rf ./iso
   ```

### Build Verification

Verify your custom build:

```bash
# Check ISO file
file custom-mikrotik-*.iso

# Mount and inspect
sudo mkdir -p /mnt/custom_iso
sudo mount -o loop custom-mikrotik-*.iso /mnt/custom_iso
ls -la /mnt/custom_iso/
sudo umount /mnt/custom_iso
```

## Testing

### Virtual Machine Testing

Test your custom RouterOS in a VM:

1. Create new VM with 1GB+ RAM
2. Boot from custom ISO
3. Install RouterOS
4. Test license generation and activation
5. Verify custom branding and email

### License Testing

```bash
# Generate test license
./generate_license.sh TEST-0001 7 4

# The output will show if the license is valid
# with your custom keys
```

## Production Deployment

### Server Setup

For production use, set up your own servers:

1. **License Server**: Handle license validation
2. **Upgrade Server**: Provide RouterOS updates  
3. **Renew Server**: Handle license renewals
4. **Cloud Server**: Cloud backup and sync

### Network Configuration

Configure your RouterOS to use custom servers:
- Update DNS settings
- Configure firewall rules
- Set up SSL certificates

## Conclusion

This guide provides a complete solution for creating custom RouterOS builds with:

✅ **Custom Email**: `<EMAIL>` replaces `<EMAIL>`  
✅ **Custom Keys**: Your own cryptographic keys for licensing  
✅ **Custom Branding**: Updated logos and system information  
✅ **Multi-Architecture**: Support for x86 and ARM64  
✅ **License Generation**: Create valid licenses for your build  
✅ **Automated Process**: Scripts handle the entire build process  

Your custom RouterOS will accept licenses generated with your private keys and display your custom branding throughout the system.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify all dependencies are installed
3. Ensure you have the required MikroTik public keys
4. Test in a virtual machine first

Remember: This is for educational and research purposes. Always comply with applicable laws and terms of service.
