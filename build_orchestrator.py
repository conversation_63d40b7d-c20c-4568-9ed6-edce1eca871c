#!/usr/bin/env python3
"""
RouterOS Build Orchestrator
Comprehensive system to orchestrate the entire custom RouterOS build process.
"""

import os
import sys
import json
import shutil
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime
from email_replacer import EmailReplacer
from license_manager import LicenseManager

class BuildOrchestrator:
    """Orchestrates the complete RouterOS build process"""
    
    def __init__(self, config_file=None):
        self.config = self.load_config(config_file)
        self.build_dir = Path(self.config.get('build_dir', './build'))
        self.output_dir = Path(self.config.get('output_dir', './output'))
        self.temp_dir = None
        self.license_manager = LicenseManager()
        
        # Create directories
        self.build_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
    
    def load_config(self, config_file):
        """Load build configuration"""
        default_config = {
            'routeros_version': '7.19.4',
            'architectures': ['x86', 'arm64'],
            'old_email': '<EMAIL>',
            'new_email': '<EMAIL>',
            'custom_urls': {
                'license': 'your-license-server.com',
                'upgrade': 'your-upgrade-server.com',
                'renew': 'your-renew-server.com',
                'cloud': 'your-cloud-server.com'
            },
            'build_options': {
                'replace_email': True,
                'replace_keys': True,
                'sign_packages': True,
                'create_iso': True,
                'generate_licenses': True
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def setup_environment(self):
        """Setup build environment variables"""
        # Get or generate keys
        private_key, public_key = self.license_manager.db.get_default_key_pair()
        
        if not private_key:
            print("Generating new key pair...")
            private_key, public_key = self.license_manager.generate_and_save_keys("build", is_default=True)
        
        # Set environment variables
        env_vars = {
            'CUSTOM_LICENSE_PRIVATE_KEY': private_key,
            'CUSTOM_LICENSE_PUBLIC_KEY': public_key,
            'CUSTOM_NPK_SIGN_PRIVATE_KEY': private_key,
            'CUSTOM_NPK_SIGN_PUBLIC_KEY': public_key,
            'CUSTOM_CLOUD_PUBLIC_KEY': public_key,
            'CUSTOM_EMAIL': self.config['new_email'],
            'ORIGINAL_EMAIL': self.config['old_email'],
            'ROUTEROS_VERSION': self.config['routeros_version'],
            'CUSTOM_LICENCE_URL': self.config['custom_urls']['license'],
            'CUSTOM_UPGRADE_URL': self.config['custom_urls']['upgrade'],
            'CUSTOM_RENEW_URL': self.config['custom_urls']['renew'],
            'CUSTOM_CLOUD_URL': self.config['custom_urls']['cloud']
        }
        
        # Update environment
        for key, value in env_vars.items():
            os.environ[key] = str(value)
        
        print("Build environment configured:")
        print(f"  RouterOS Version: {self.config['routeros_version']}")
        print(f"  Email: {self.config['old_email']} -> {self.config['new_email']}")
        print(f"  Private Key: {private_key[:16]}...")
        print(f"  Public Key: {public_key[:16]}...")
        
        return env_vars
    
    def download_routeros(self, version, arch):
        """Download RouterOS ISO"""
        if arch == 'x86':
            url = f"https://download.mikrotik.com/routeros/{version}/mikrotik-{version}.iso"
            filename = f"mikrotik-{version}.iso"
        elif arch == 'arm64':
            url = f"https://download.mikrotik.com/routeros/{version}/mikrotik-{version}-arm64.iso"
            filename = f"mikrotik-{version}-arm64.iso"
        else:
            raise ValueError(f"Unsupported architecture: {arch}")
        
        iso_path = self.build_dir / filename
        
        if iso_path.exists():
            print(f"Using existing ISO: {iso_path}")
            return iso_path
        
        print(f"Downloading {filename}...")
        try:
            subprocess.run(['wget', '-O', str(iso_path), url], check=True)
            print(f"Downloaded: {iso_path}")
            return iso_path
        except subprocess.CalledProcessError:
            print(f"Failed to download {url}")
            return None
    
    def extract_iso(self, iso_path, extract_dir):
        """Extract ISO contents"""
        print(f"Extracting ISO: {iso_path}")
        
        mount_dir = self.build_dir / 'iso_mount'
        mount_dir.mkdir(exist_ok=True)
        
        try:
            # Mount ISO
            subprocess.run(['sudo', 'mount', '-o', 'loop,ro', str(iso_path), str(mount_dir)], check=True)
            
            # Copy contents
            subprocess.run(['sudo', 'cp', '-r', f"{mount_dir}/*", str(extract_dir)], shell=True, check=True)
            subprocess.run(['sudo', 'rsync', '-a', f"{mount_dir}/", str(extract_dir)], check=True)
            
            # Unmount
            subprocess.run(['sudo', 'umount', str(mount_dir)], check=True)
            
            print(f"Extracted to: {extract_dir}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Failed to extract ISO: {e}")
            return False
        finally:
            # Cleanup mount point
            if mount_dir.exists():
                subprocess.run(['sudo', 'rmdir', str(mount_dir)], check=False)
    
    def patch_system_package(self, extract_dir, version):
        """Patch system NPK package"""
        system_npk = extract_dir / f"system-{version}.npk"
        
        if not system_npk.exists():
            print(f"System package not found: {system_npk}")
            return False
        
        print(f"Patching system package: {system_npk}")
        
        try:
            # Move to working directory for patching
            work_npk = self.build_dir / f"system-{version}.npk"
            subprocess.run(['sudo', 'mv', str(system_npk), str(work_npk)], check=True)
            
            # Patch the package
            subprocess.run(['sudo', '-E', 'python3', 'patch.py', 'npk', str(work_npk)], check=True)
            
            # Move back
            subprocess.run(['sudo', 'cp', str(work_npk), str(system_npk)], check=True)
            
            print("System package patched successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Failed to patch system package: {e}")
            return False
    
    def replace_emails(self, extract_dir):
        """Replace email addresses in extracted files"""
        if not self.config['build_options']['replace_email']:
            return True
        
        print("Replacing email addresses...")
        
        replacer = EmailReplacer(
            self.config['old_email'],
            self.config['new_email']
        )
        
        replacer.replace_in_directory(str(extract_dir))
        return True
    
    def sign_packages(self, extract_dir):
        """Sign all NPK packages"""
        if not self.config['build_options']['sign_packages']:
            return True
        
        print("Signing NPK packages...")
        
        npk_files = list(extract_dir.glob("*.npk"))
        
        for npk_file in npk_files:
            try:
                print(f"  Signing: {npk_file.name}")
                subprocess.run(['sudo', '-E', 'python3', 'npk.py', 'sign', str(npk_file), str(npk_file)], check=True)
            except subprocess.CalledProcessError as e:
                print(f"  Failed to sign {npk_file}: {e}")
        
        return True
    
    def create_iso(self, extract_dir, arch, version):
        """Create custom ISO"""
        if not self.config['build_options']['create_iso']:
            return None
        
        output_iso = self.output_dir / f"custom-mikrotik-{version}-{arch}.iso"
        
        print(f"Creating custom ISO: {output_iso}")
        
        try:
            if arch == 'x86':
                subprocess.run([
                    'sudo', 'mkisofs', '-o', str(output_iso),
                    '-V', f'Custom MikroTik {version}',
                    '-sysid', '', '-preparer', 'Custom MikroTik',
                    '-publisher', '', '-A', 'Custom MikroTik RouterOS',
                    '-input-charset', 'utf-8',
                    '-b', 'isolinux/isolinux.bin',
                    '-c', 'isolinux/boot.cat',
                    '-no-emul-boot',
                    '-boot-load-size', '4',
                    '-boot-info-table',
                    '-R', '-J',
                    str(extract_dir)
                ], check=True)
            
            elif arch == 'arm64':
                subprocess.run([
                    'sudo', 'xorriso', '-as', 'mkisofs', '-o', str(output_iso),
                    '-V', f'Custom MikroTik {version} ARM64',
                    '-sysid', '', '-preparer', 'Custom MikroTik',
                    '-publisher', '', '-A', 'Custom MikroTik RouterOS',
                    '-input-charset', 'utf-8',
                    '-b', 'efiboot.img',
                    '-no-emul-boot',
                    '-R', '-J',
                    str(extract_dir)
                ], check=True)
            
            print(f"Custom ISO created: {output_iso}")
            return output_iso
            
        except subprocess.CalledProcessError as e:
            print(f"Failed to create ISO: {e}")
            return None
    
    def generate_sample_licenses(self):
        """Generate sample licenses for the build"""
        if not self.config['build_options']['generate_licenses']:
            return
        
        print("Generating sample licenses...")
        
        licenses_dir = self.output_dir / 'licenses'
        licenses_dir.mkdir(exist_ok=True)
        
        # Generate different license types
        license_types = [
            {'software_id': 'CUST-OM01', 'version': 7, 'level': 4, 'name': 'standard'},
            {'software_id': 'CUST-OM02', 'version': 7, 'level': 6, 'name': 'unlimited'},
            {'software_id': 'TEST-0001', 'version': 7, 'level': 1, 'name': 'limited'}
        ]
        
        for license_type in license_types:
            license_id, license_key = self.license_manager.create_license(
                software_id=license_type['software_id'],
                version=license_type['version'],
                level=license_type['level']
            )
            
            license_file = licenses_dir / f"{license_type['name']}_license.key"
            with open(license_file, 'w') as f:
                f.write(license_key)
            
            print(f"  Generated: {license_file}")
    
    def build_architecture(self, arch):
        """Build custom RouterOS for specific architecture"""
        version = self.config['routeros_version']
        
        print(f"\n{'='*60}")
        print(f"Building RouterOS {version} for {arch}")
        print(f"{'='*60}")
        
        # Download ISO
        iso_path = self.download_routeros(version, arch)
        if not iso_path:
            return False
        
        # Create extraction directory
        extract_dir = self.build_dir / f"extracted_{arch}"
        if extract_dir.exists():
            shutil.rmtree(extract_dir)
        extract_dir.mkdir()
        
        # Extract ISO
        if not self.extract_iso(iso_path, extract_dir):
            return False
        
        # Patch system package
        if not self.patch_system_package(extract_dir, version):
            return False
        
        # Replace emails
        if not self.replace_emails(extract_dir):
            return False
        
        # Sign packages
        if not self.sign_packages(extract_dir):
            return False
        
        # Create custom ISO
        output_iso = self.create_iso(extract_dir, arch, version)
        
        # Cleanup extraction directory
        shutil.rmtree(extract_dir)
        
        return output_iso is not None
    
    def build_all(self):
        """Build custom RouterOS for all configured architectures"""
        print("Starting RouterOS build process...")
        
        # Setup environment
        self.setup_environment()
        
        # Build each architecture
        success_count = 0
        for arch in self.config['architectures']:
            if self.build_architecture(arch):
                success_count += 1
        
        # Generate sample licenses
        self.generate_sample_licenses()
        
        # Create build report
        self.create_build_report(success_count)
        
        print(f"\nBuild completed: {success_count}/{len(self.config['architectures'])} successful")
        return success_count == len(self.config['architectures'])
    
    def create_build_report(self, success_count):
        """Create build report"""
        report = {
            'build_time': datetime.now().isoformat(),
            'routeros_version': self.config['routeros_version'],
            'architectures': self.config['architectures'],
            'successful_builds': success_count,
            'email_replacement': f"{self.config['old_email']} -> {self.config['new_email']}",
            'output_files': list(str(f) for f in self.output_dir.glob('*'))
        }
        
        report_file = self.output_dir / 'build_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Build report saved: {report_file}")

def main():
    """Main build orchestrator"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RouterOS Build Orchestrator')
    parser.add_argument('--config', help='Configuration file')
    parser.add_argument('--arch', choices=['x86', 'arm64'], help='Build specific architecture only')
    
    args = parser.parse_args()
    
    orchestrator = BuildOrchestrator(args.config)
    
    if args.arch:
        # Build specific architecture
        orchestrator.setup_environment()
        success = orchestrator.build_architecture(args.arch)
        orchestrator.generate_sample_licenses()
        orchestrator.create_build_report(1 if success else 0)
    else:
        # Build all architectures
        success = orchestrator.build_all()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
