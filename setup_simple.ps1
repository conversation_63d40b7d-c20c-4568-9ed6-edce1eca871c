# Simple Windows Setup for MikroTik License Generator
param(
    [string]$CustomEmail = "<EMAIL>"
)

Write-Host "=========================================="
Write-Host "MikroTik License Generator Setup (Windows)"
Write-Host "=========================================="

# Generate keys
Write-Host "Generating custom keys..." -ForegroundColor Yellow
$keyOutput = python mikrotik_keygen.py --generate-keys

# Extract keys
$lines = $keyOutput -split "`n"
$privateKey = ""
$publicKey = ""

foreach ($line in $lines) {
    if ($line -match "Private Key: (.+)") {
        $privateKey = $matches[1]
    }
    if ($line -match "Public Key: (.+)") {
        $publicKey = $matches[1]
    }
}

if ($privateKey -and $publicKey) {
    Write-Host "Keys generated successfully!" -ForegroundColor Green
    Write-Host "Private Key: $($privateKey.Substring(0,16))..."
    Write-Host "Public Key:  $($publicKey.Substring(0,16))..."
} else {
    Write-Host "Failed to generate keys" -ForegroundColor Red
    exit 1
}

# Create environment file
$envContent = @"
# Custom Environment Variables
`$env:CUSTOM_LICENSE_PRIVATE_KEY = "$privateKey"
`$env:CUSTOM_LICENSE_PUBLIC_KEY = "$publicKey"
`$env:CUSTOM_EMAIL = "$CustomEmail"

Write-Host "Environment loaded!" -ForegroundColor Green
"@

$envContent | Out-File -FilePath "env.ps1" -Encoding UTF8

# Create license generator
$licenseContent = @"
param([string]`$SoftwareID = "CUST-OM01", [int]`$Version = 7, [int]`$Level = 4)
. .\env.ps1
python mikrotik_keygen.py --software-id `$SoftwareID --version `$Version --level `$Level --private-key `$env:CUSTOM_LICENSE_PRIVATE_KEY
"@

$licenseContent | Out-File -FilePath "gen_license.ps1" -Encoding UTF8

Write-Host ""
Write-Host "Setup completed!" -ForegroundColor Green
Write-Host "Files created:"
Write-Host "  - env.ps1 (environment)"
Write-Host "  - gen_license.ps1 (license generator)"
Write-Host ""
Write-Host "Usage:"
Write-Host "  .\gen_license.ps1 -SoftwareID 'TEST-0001' -Version 7 -Level 4"
Write-Host "  python mikrotik_keygen_gui.py"
Write-Host "  python quick_keygen.py"
