#!/usr/bin/env python3
"""
MikroTik License Key Generator - GUI Version
A graphical interface for generating valid MikroTik software keys.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import random
import secrets
from mikrotik_keygen import MikroTikLicenseGenerator

class MikroTikKeygenGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MikroTik License Key Generator")
        self.root.geometry("800x700")
        
        # Initialize generator
        self.generator = MikroTikLicenseGenerator()
        
        self.create_widgets()
    
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="MikroTik License Key Generator", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Input section
        input_frame = ttk.LabelFrame(main_frame, text="License Parameters", padding="10")
        input_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Software ID
        ttk.Label(input_frame, text="Software ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.software_id_var = tk.StringVar()
        self.software_id_entry = ttk.Entry(input_frame, textvariable=self.software_id_var, width=15)
        self.software_id_entry.grid(row=0, column=1, sticky=tk.W, padx=(5, 10), pady=2)
        
        ttk.Button(input_frame, text="Generate Random", 
                  command=self.generate_random_software_id).grid(row=0, column=2, pady=2)
        
        # Version
        ttk.Label(input_frame, text="Version:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.version_var = tk.StringVar(value="7")
        version_combo = ttk.Combobox(input_frame, textvariable=self.version_var, 
                                   values=["6", "7"], width=12, state="readonly")
        version_combo.grid(row=1, column=1, sticky=tk.W, padx=(5, 10), pady=2)
        
        # Level
        ttk.Label(input_frame, text="Level:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.level_var = tk.StringVar(value="4")
        level_combo = ttk.Combobox(input_frame, textvariable=self.level_var,
                                 values=["1", "2", "3", "4", "5", "6"], width=12, state="readonly")
        level_combo.grid(row=2, column=1, sticky=tk.W, padx=(5, 10), pady=2)
        
        # Private Key
        ttk.Label(input_frame, text="Private Key (hex):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.private_key_var = tk.StringVar()
        self.private_key_entry = ttk.Entry(input_frame, textvariable=self.private_key_var, width=50)
        self.private_key_entry.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(5, 0), pady=2)
        
        ttk.Button(input_frame, text="Generate Key Pair", 
                  command=self.generate_key_pair).grid(row=4, column=0, columnspan=3, pady=10)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="Generate License", 
                  command=self.generate_license).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Clear", 
                  command=self.clear_output).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Save License", 
                  command=self.save_license).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Load & Verify", 
                  command=self.load_and_verify).pack(side=tk.LEFT)
        
        # Output section
        output_frame = ttk.LabelFrame(main_frame, text="Generated License", padding="10")
        output_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80, 
                                                   font=("Courier", 10))
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        input_frame.columnconfigure(1, weight=1)
    
    def generate_random_software_id(self):
        """Generate a random software ID"""
        software_id = self.generator.generate_software_id()
        self.software_id_var.set(software_id)
        self.status_var.set(f"Generated random Software ID: {software_id}")
    
    def generate_key_pair(self):
        """Generate a new key pair"""
        try:
            from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
            from cryptography.hazmat.primitives import serialization
            
            # Generate new key pair
            private_key = Ed25519PrivateKey.generate()
            public_key = private_key.public_key()
            
            private_bytes = private_key.private_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PrivateFormat.Raw,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            public_bytes = public_key.public_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PublicFormat.Raw
            )
            
            # Set private key
            self.private_key_var.set(private_bytes.hex())
            
            # Show key pair in dialog
            key_info = f"Private Key: {private_bytes.hex()}\n\nPublic Key: {public_bytes.hex()}"
            messagebox.showinfo("Generated Key Pair", key_info)
            
            self.status_var.set("New key pair generated")
            
        except ImportError:
            messagebox.showerror("Error", "cryptography library not available")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate key pair: {e}")
    
    def generate_license(self):
        """Generate a license key"""
        try:
            # Get parameters
            software_id = self.software_id_var.get().strip() or None
            version = int(self.version_var.get())
            level = int(self.level_var.get())
            private_key = self.private_key_var.get().strip() or None
            
            # Create generator with private key
            if private_key:
                generator = MikroTikLicenseGenerator(private_key)
            else:
                generator = MikroTikLicenseGenerator()
            
            # Generate license
            license_key = generator.create_license_key(software_id, version, level)
            
            # Display in output
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(1.0, license_key)
            
            # Update status
            if private_key:
                self.status_var.set("License generated successfully with provided key")
            else:
                self.status_var.set("License generated with random key (for testing only)")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate license: {e}")
            self.status_var.set("Error generating license")
    
    def clear_output(self):
        """Clear the output text"""
        self.output_text.delete(1.0, tk.END)
        self.status_var.set("Output cleared")
    
    def save_license(self):
        """Save the license to a file"""
        license_content = self.output_text.get(1.0, tk.END).strip()
        if not license_content:
            messagebox.showwarning("Warning", "No license to save")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".key",
            filetypes=[("License files", "*.key"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(license_content)
                self.status_var.set(f"License saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save license: {e}")
    
    def load_and_verify(self):
        """Load and verify a license file"""
        filename = filedialog.askopenfilename(
            filetypes=[("License files", "*.key"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    license_content = f.read()
                
                # Display license
                self.output_text.delete(1.0, tk.END)
                self.output_text.insert(1.0, license_content)
                
                # Ask for public key for verification
                public_key = simpledialog.askstring("Public Key",
                                                   "Enter public key (hex) for verification:")
                
                if public_key:
                    generator = MikroTikLicenseGenerator()
                    is_valid = generator.verify_license(license_content, public_key)
                    
                    if is_valid:
                        messagebox.showinfo("Verification", "License is VALID")
                        self.status_var.set("License loaded and verified successfully")
                    else:
                        messagebox.showwarning("Verification", "License is INVALID")
                        self.status_var.set("License loaded but verification failed")
                else:
                    self.status_var.set("License loaded (not verified)")
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load license: {e}")

def main():
    root = tk.Tk()
    app = MikroTikKeygenGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
