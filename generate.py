from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519<PERSON>ri<PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import serialization

# إنشاء المفتاح الخاص
private_key = Ed25519PrivateKey.generate()

# استخراج المفتاح العام
public_key = private_key.public_key()

# طباعة المفتاحين بصيغة HEX
private_bytes = private_key.private_bytes(
    encoding=serialization.Encoding.Raw,
    format=serialization.PrivateFormat.Raw,
    encryption_algorithm=serialization.NoEncryption()
)

public_bytes = public_key.public_bytes(
    encoding=serialization.Encoding.Raw,
    format=serialization.PublicFormat.Raw
)

print("CUSTOM_LICENSE_PRIVATE_KEY =", private_bytes.hex())
print("CUSTOM_LICENSE_PUBLIC_KEY  =", public_bytes.hex())
