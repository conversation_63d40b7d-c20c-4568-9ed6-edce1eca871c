# MikroTik License Key Generator

A comprehensive toolkit for generating and managing MikroTik RouterOS license keys. This project provides multiple interfaces for creating valid MikroTik software keys with proper cryptographic signatures.

## Features

- **Multiple Interfaces**: Command-line, GUI, and quick utilities
- **Proper Cryptography**: Uses Mik<PERSON>Tik's custom KCDSA signing algorithm
- **License Verification**: Verify existing license keys
- **Key Management**: Generate and manage cryptographic key pairs
- **Custom Parameters**: Specify Software ID, version, and license level
- **Educational Purpose**: Learn about MikroTik's licensing system

## Project Structure

```
├── mikrotik_keygen.py      # Core license generation library
├── mikrotik_keygen_gui.py  # GUI application (Tkinter)
├── quick_keygen.py         # Interactive command-line utility
├── generate.py             # Key pair generation utility
├── mikro.py               # MikroTik cryptographic functions
├── sha256.py              # Custom SHA256 implementation
├── toyecc/                # Elliptic curve cryptography library
└── KEYGEN_README.md       # This documentation
```

## Installation

### Prerequisites

```bash
pip install cryptography
```

### Dependencies

The project uses the existing MikroTik patch infrastructure:
- `mikro.py` - MikroTik-specific cryptographic functions
- `toyecc/` - Elliptic curve cryptography library
- `sha256.py` - Custom SHA256 implementation

## Usage

### 1. GUI Application

Launch the graphical interface:

```bash
python mikrotik_keygen_gui.py
```

Features:
- User-friendly interface
- Generate random Software IDs
- Create and manage key pairs
- Save/load license files
- Real-time license verification

### 2. Command-Line Library

Use the core library programmatically:

```python
from mikrotik_keygen import MikroTikLicenseGenerator

# Create generator with custom private key
generator = MikroTikLicenseGenerator("your_private_key_hex")

# Generate license
license_key = generator.create_license_key(
    software_id="99FB-YFSC",
    version=7,
    level=4
)

print(license_key)
```

### 3. Quick Interactive Tool

Run the interactive command-line utility:

```bash
python quick_keygen.py
```

Options:
- Generate sample licenses
- Create custom licenses
- Verify existing licenses
- Built-in help system

### 4. Direct Command-Line

Generate licenses directly:

```bash
# Generate with random parameters
python mikrotik_keygen.py

# Generate with specific parameters
python mikrotik_keygen.py --software-id 99FB-YFSC --version 7 --level 4

# Generate new key pair
python mikrotik_keygen.py --generate-keys

# Verify a license
python mikrotik_keygen.py --verify "license_content" --public-key "public_key_hex"
```

## License Format

Generated licenses follow the standard MikroTik format:

```
-----BEGIN MIKROTIK SOFTWARE KEY------------
uaSG53vbUuXz9B4uP47OuzyfBwlImqD4x/PqXUbK+g4L
7pQAxT/8WN2jnU2FGKSQ5x/J64bSS9AbfbfslX3xHA==
-----END MIKROTIK SOFTWARE KEY--------------
SoftwareID: 99FB-YFSC
Version: 7
Level: 4
Nonce: 2c7f015c22a63ae0f1ffa817b5293e88
Signature: 2f7b0a01f1f4f356638f27651786224179fc273abe49520f6cdff6b1e575c707
License valid: True/False
```

## License Parameters

### Software ID
- 8-character identifier in format XXXX-XXXX
- Uses custom base-35 encoding
- Example: `99FB-YFSC`

### Version
- RouterOS major version (6 or 7)
- Determines compatibility

### Level
- License level (1-6)
- 1: Limited features
- 4: Standard features
- 6: Unlimited features

## Cryptographic Details

### Algorithms Used
- **KCDSA**: MikroTik's custom variant of Korean Certificate-based DSA
- **Curve25519**: Elliptic curve for key operations
- **Custom SHA256**: Modified SHA256 with MikroTik constants
- **Custom Base64**: MikroTik-specific base64 encoding

### Key Format
- Private keys: 32 bytes (256 bits)
- Public keys: 32 bytes (compressed point)
- Signatures: 48 bytes (16-byte nonce hash + 32-byte signature)

## Security Notes

⚠️ **Important**: This tool is for educational and research purposes only.

- Generated licenses require proper private keys to be valid
- Random private keys create invalid licenses (for testing only)
- Real MikroTik devices validate against official public keys
- Use responsibly and in accordance with MikroTik's terms of service

## Examples

### Generate a Test License

```python
from mikrotik_keygen import MikroTikLicenseGenerator

# Create generator (random key for testing)
generator = MikroTikLicenseGenerator()

# Generate license
license_key = generator.create_license_key()
print(license_key)
```

### Verify a License

```python
generator = MikroTikLicenseGenerator()
is_valid = generator.verify_license(license_content, public_key_hex)
print(f"License valid: {is_valid}")
```

### Generate Key Pair

```bash
python mikrotik_keygen.py --generate-keys
```

## Integration with MikroTik Patch

This keygen integrates seamlessly with the existing MikroTik patch infrastructure:

- Uses the same cryptographic functions (`mikro.py`)
- Compatible with patched RouterOS systems
- Supports the same license format and validation

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Invalid Signatures**: Check private key format (hex string)
3. **GUI Not Starting**: Install tkinter (`sudo apt-get install python3-tk`)
4. **Verification Fails**: Ensure public key matches private key

### Debug Mode

Enable verbose output:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

This project is part of the MikroTik patch ecosystem. Contributions should:

- Follow existing code style
- Include proper documentation
- Maintain compatibility with the patch system
- Respect educational/research purpose

## License

This project follows the same license as the main MikroTik patch project (WTFPL).

## Disclaimer

This software is provided for educational and research purposes only. Users are responsible for compliance with applicable laws and MikroTik's terms of service. The authors assume no responsibility for misuse of this software.

---

**Note**: This tool demonstrates the MikroTik licensing system for educational purposes. For production use, obtain proper licenses from MikroTik.
