# Custom RouterOS Build Setup for Windows
# PowerShell script to set up the environment on Windows

param(
    [string]$RouterOSVersion = "7.19.4",
    [string]$CustomEmail = "<EMAIL>",
    [string]$OriginalEmail = "<EMAIL>"
)

Write-Host "=========================================="
Write-Host "Custom RouterOS Build Setup (Windows)"
Write-Host "=========================================="

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

Write-Host "Setting up custom RouterOS build environment..." -ForegroundColor $Blue

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python found: $pythonVersion" -ForegroundColor $Green
} catch {
    Write-Host "✗ Python not found. Please install Python 3.8+" -ForegroundColor $Red
    exit 1
}

# Check if pip is available
try {
    pip --version | Out-Null
    Write-Host "✓ pip found" -ForegroundColor $Green
} catch {
    Write-Host "✗ pip not found. Please install pip" -ForegroundColor $Red
    exit 1
}

# Install required Python packages
Write-Host "Installing Python packages..." -ForegroundColor $Yellow
try {
    pip install cryptography | Out-Null
    Write-Host "✓ cryptography installed" -ForegroundColor $Green
} catch {
    Write-Host "⚠ Failed to install cryptography" -ForegroundColor $Yellow
}

# Generate custom keys
Write-Host "Generating custom cryptographic keys..." -ForegroundColor $Yellow
$keyOutput = python mikrotik_keygen.py --generate-keys 2>&1

# Extract keys from output
$licensePrivateKey = ($keyOutput | Select-String "Private Key: (.+)" | ForEach-Object { $_.Matches[0].Groups[1].Value })
$licensePublicKey = ($keyOutput | Select-String "Public Key: (.+)" | ForEach-Object { $_.Matches[0].Groups[1].Value })

if ($licensePrivateKey -and $licensePublicKey) {
    Write-Host "✓ Keys generated successfully!" -ForegroundColor $Green
    Write-Host "License Private Key: $($licensePrivateKey.Substring(0,16))..." -ForegroundColor $Blue
    Write-Host "License Public Key:  $($licensePublicKey.Substring(0,16))..." -ForegroundColor $Blue
} else {
    Write-Host "✗ Failed to generate keys" -ForegroundColor $Red
    exit 1
}

# Create environment configuration file
Write-Host "Creating environment configuration..." -ForegroundColor $Yellow

$envConfig = @"
# Custom RouterOS Build Environment Variables (Windows)
# Generated on $(Get-Date)

# Custom Keys
`$env:CUSTOM_LICENSE_PRIVATE_KEY = "$licensePrivateKey"
`$env:CUSTOM_LICENSE_PUBLIC_KEY = "$licensePublicKey"
`$env:CUSTOM_NPK_SIGN_PRIVATE_KEY = "$licensePrivateKey"
`$env:CUSTOM_NPK_SIGN_PUBLIC_KEY = "$licensePublicKey"
`$env:CUSTOM_CLOUD_PUBLIC_KEY = "$licensePublicKey"

# Original MikroTik Keys (you need to obtain these)
`$env:MIKRO_LICENSE_PUBLIC_KEY = "original_mikrotik_license_public_key_here"
`$env:MIKRO_NPK_SIGN_PUBLIC_KEY = "original_mikrotik_npk_public_key_here"
`$env:MIKRO_CLOUD_PUBLIC_KEY = "original_mikrotik_cloud_public_key_here"

# URLs - replace with your own servers
`$env:MIKRO_LICENCE_URL = "licence.mikrotik.com"
`$env:CUSTOM_LICENCE_URL = "your-license-server.com"
`$env:MIKRO_UPGRADE_URL = "upgrade.mikrotik.com"
`$env:CUSTOM_UPGRADE_URL = "your-upgrade-server.com"
`$env:MIKRO_RENEW_URL = "renew.mikrotik.com"
`$env:CUSTOM_RENEW_URL = "your-renew-server.com"
`$env:MIKRO_CLOUD_URL = "cloud.mikrotik.com"
`$env:CUSTOM_CLOUD_URL = "your-cloud-server.com"

# Custom settings
`$env:CUSTOM_EMAIL = "$CustomEmail"
`$env:ORIGINAL_EMAIL = "$OriginalEmail"
`$env:ROUTEROS_VERSION = "$RouterOSVersion"

Write-Host "Custom RouterOS build environment loaded!" -ForegroundColor Green
"@

$envConfig | Out-File -FilePath "custom_build_env.ps1" -Encoding UTF8

# Create license generator script
Write-Host "Creating license generator script..." -ForegroundColor $Yellow

$licenseScript = @"
# Generate License Script for Windows
param(
    [string]`$SoftwareID = "CUST-OM01",
    [int]`$Version = 7,
    [int]`$Level = 4
)

# Load environment
. .\custom_build_env.ps1

Write-Host "Generating license for Software ID: `$SoftwareID"

python mikrotik_keygen.py ``
    --software-id `$SoftwareID ``
    --version `$Version ``
    --level `$Level ``
    --private-key `$env:CUSTOM_LICENSE_PRIVATE_KEY

Write-Host ""
Write-Host "License generated with your custom private key!" -ForegroundColor Green
Write-Host "This license will work with your custom RouterOS build." -ForegroundColor Green
"@

$licenseScript | Out-File -FilePath "generate_license.ps1" -Encoding UTF8

# Create demo script
Write-Host "Creating demo script..." -ForegroundColor $Yellow

$demoScript = @"
# Demo Script for Windows
Write-Host "Running MikroTik License Key Generator Demo..." -ForegroundColor Blue

# Load environment
. .\custom_build_env.ps1

# Generate sample licenses
Write-Host "Generating sample licenses..." -ForegroundColor Yellow

Write-Host "1. Standard License:" -ForegroundColor Green
python mikrotik_keygen.py --software-id Q0HT-TTTT --version 7 --level 4 --private-key `$env:CUSTOM_LICENSE_PRIVATE_KEY

Write-Host "`n2. Unlimited License:" -ForegroundColor Green  
python mikrotik_keygen.py --software-id ABCD-EFGH --version 7 --level 6 --private-key `$env:CUSTOM_LICENSE_PRIVATE_KEY

Write-Host "`n3. Random Software ID:" -ForegroundColor Green
python mikrotik_keygen.py --version 7 --level 4 --private-key `$env:CUSTOM_LICENSE_PRIVATE_KEY

Write-Host "`nDemo completed!" -ForegroundColor Blue
"@

$demoScript | Out-File -FilePath "demo_windows.ps1" -Encoding UTF8

# Create test script
Write-Host "Creating test script..." -ForegroundColor $Yellow

$testScript = @"
# Test Script for Windows
Write-Host "Testing MikroTik License Key Generator..." -ForegroundColor Blue

# Test basic functionality
Write-Host "Testing license generation..." -ForegroundColor Yellow
try {
    `$result = python mikrotik_keygen.py --software-id Q0HT-TTTT --version 7 --level 4 2>&1
    if (`$result -match "-----BEGIN MIKROTIK SOFTWARE KEY------------") {
        Write-Host "✓ License generation works" -ForegroundColor Green
    } else {
        Write-Host "✗ License generation failed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ License generation error: `$_" -ForegroundColor Red
}

# Test key generation
Write-Host "Testing key generation..." -ForegroundColor Yellow
try {
    `$keyResult = python mikrotik_keygen.py --generate-keys 2>&1
    if (`$keyResult -match "Private Key:") {
        Write-Host "✓ Key generation works" -ForegroundColor Green
    } else {
        Write-Host "✗ Key generation failed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Key generation error: `$_" -ForegroundColor Red
}

# Test GUI (if available)
Write-Host "Testing GUI availability..." -ForegroundColor Yellow
try {
    `$guiTest = python -c "import tkinter; print('GUI available')" 2>&1
    if (`$guiTest -match "GUI available") {
        Write-Host "✓ GUI available (run: python mikrotik_keygen_gui.py)" -ForegroundColor Green
    } else {
        Write-Host "⚠ GUI not available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ GUI test failed" -ForegroundColor Yellow
}

Write-Host "Test completed!" -ForegroundColor Blue
"@

$testScript | Out-File -FilePath "test_windows.ps1" -Encoding UTF8

Write-Host "✓ Setup completed successfully!" -ForegroundColor $Green
Write-Host ""
Write-Host "Files created:" -ForegroundColor $Blue
Write-Host "  - custom_build_env.ps1     (Environment variables)"
Write-Host "  - generate_license.ps1     (License generator)"
Write-Host "  - demo_windows.ps1         (Demo script)"
Write-Host "  - test_windows.ps1         (Test script)"
Write-Host ""
Write-Host "Usage:" -ForegroundColor $Blue
Write-Host "  1. Test system:       .\test_windows.ps1"
Write-Host "  2. Run demo:          .\demo_windows.ps1"
Write-Host "  3. Generate license:  .\generate_license.ps1 -SoftwareID 'CUST-OM01' -Version 7 -Level 4"
Write-Host "  4. GUI application:   python mikrotik_keygen_gui.py"
Write-Host "  5. Interactive tool:  python quick_keygen.py"
Write-Host ""
Write-Host "Important Notes:" -ForegroundColor $Yellow
Write-Host "  - Your custom email: $CustomEmail"
Write-Host "  - Custom keys have been generated and saved"
Write-Host "  - For full RouterOS building, you need a Linux environment"
Write-Host "  - On Windows, you can generate licenses and test functionality"
Write-Host ""
Write-Host "Ready to generate MikroTik licenses!" -ForegroundColor Green
