#!/usr/bin/env python3
"""
MikroTik License Key Generator - Demonstration
Shows complete functionality with examples.
"""

from mikrotik_keygen import MikroTikLicenseGenerator
from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
from cryptography.hazmat.primitives import serialization

def print_banner():
    """Print demonstration banner"""
    print("=" * 70)
    print("    MikroTik License Key Generator - Demonstration")
    print("=" * 70)
    print()

def demo_key_generation():
    """Demonstrate key pair generation"""
    print("1. KEY PAIR GENERATION")
    print("-" * 30)
    
    # Generate new key pair
    private_key = Ed25519PrivateKey.generate()
    public_key = private_key.public_key()
    
    private_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PrivateFormat.Raw,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    public_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PublicFormat.Raw
    )
    
    print(f"Generated Private Key: {private_bytes.hex()}")
    print(f"Generated Public Key:  {public_bytes.hex()}")
    print()
    
    return private_bytes.hex(), public_bytes.hex()

def demo_license_generation(private_key_hex):
    """Demonstrate license generation"""
    print("2. LICENSE GENERATION")
    print("-" * 30)
    
    # Create generator with the private key
    generator = MikroTikLicenseGenerator(private_key_hex)
    
    # Generate different types of licenses
    licenses = [
        {"software_id": "99FB-YFSC", "version": 7, "level": 4, "desc": "Standard RouterOS 7 License"},
        {"software_id": "ABCD-EFGH", "version": 6, "level": 6, "desc": "Unlimited RouterOS 6 License"},
        {"software_id": None, "version": 7, "level": 1, "desc": "Limited RouterOS 7 License (Random ID)"},
    ]
    
    generated_licenses = []
    
    for i, license_params in enumerate(licenses, 1):
        print(f"License {i}: {license_params['desc']}")
        
        license_key = generator.create_license_key(
            software_id=license_params["software_id"],
            version=license_params["version"],
            level=license_params["level"]
        )
        
        generated_licenses.append(license_key)
        
        # Show first few lines
        lines = license_key.split('\n')
        for line in lines[:3]:
            print(f"  {line}")
        print(f"  ... (truncated)")
        
        # Show license info
        for line in lines:
            if line.startswith("SoftwareID:") or line.startswith("Version:") or line.startswith("Level:"):
                print(f"  {line}")
        print()
    
    return generated_licenses

def demo_license_verification(licenses, public_key_hex):
    """Demonstrate license verification"""
    print("3. LICENSE VERIFICATION")
    print("-" * 30)
    
    generator = MikroTikLicenseGenerator()
    
    for i, license_key in enumerate(licenses, 1):
        print(f"Verifying License {i}...")
        
        try:
            is_valid = generator.verify_license(license_key, public_key_hex)
            status = "✓ VALID" if is_valid else "✗ INVALID"
            print(f"  Result: {status}")
        except Exception as e:
            print(f"  Result: ✗ ERROR - {e}")
        
        print()

def demo_software_id_generation():
    """Demonstrate software ID generation"""
    print("4. SOFTWARE ID GENERATION")
    print("-" * 30)
    
    generator = MikroTikLicenseGenerator()
    
    print("Generated random Software IDs:")
    for i in range(5):
        software_id = generator.generate_software_id()
        print(f"  {i+1}. {software_id}")
    
    print()

def demo_format_analysis():
    """Demonstrate license format analysis"""
    print("5. LICENSE FORMAT ANALYSIS")
    print("-" * 30)
    
    # Generate a sample license
    generator = MikroTikLicenseGenerator()
    license_key = generator.create_license_key("TEST-1234", 7, 4)
    
    print("License Structure:")
    lines = license_key.split('\n')
    
    for line in lines:
        if line.startswith("-----BEGIN"):
            print(f"  Header:     {line}")
        elif line.startswith("-----END"):
            print(f"  Footer:     {line}")
        elif line.startswith("SoftwareID:"):
            print(f"  Software:   {line}")
        elif line.startswith("Version:"):
            print(f"  Version:    {line}")
        elif line.startswith("Level:"):
            print(f"  Level:      {line}")
        elif line.startswith("Nonce:"):
            print(f"  Nonce:      {line}")
        elif line.startswith("Signature:"):
            print(f"  Signature:  {line}")
        elif line.startswith("License valid:"):
            print(f"  Validity:   {line}")
        elif line.strip() and not line.startswith("-----"):
            print(f"  Key Data:   {line[:20]}... ({len(line)} chars)")
    
    print()

def demo_cryptographic_details():
    """Show cryptographic implementation details"""
    print("6. CRYPTOGRAPHIC DETAILS")
    print("-" * 30)
    
    print("Algorithms Used:")
    print("  • KCDSA: MikroTik's custom Korean Certificate-based DSA variant")
    print("  • Curve25519: Elliptic curve for key operations")
    print("  • Custom SHA256: Modified SHA256 with MikroTik constants")
    print("  • Custom Base64: MikroTik-specific base64 encoding")
    print()
    
    print("Key Specifications:")
    print("  • Private Key: 32 bytes (256 bits)")
    print("  • Public Key:  32 bytes (compressed point)")
    print("  • Signature:   48 bytes (16-byte nonce hash + 32-byte signature)")
    print("  • License:     80 bytes total (32-byte payload + 48-byte signature)")
    print()

def main():
    """Main demonstration"""
    print_banner()
    
    try:
        # 1. Generate key pair
        private_key_hex, public_key_hex = demo_key_generation()
        
        # 2. Generate licenses
        licenses = demo_license_generation(private_key_hex)
        
        # 3. Verify licenses
        demo_license_verification(licenses, public_key_hex)
        
        # 4. Show software ID generation
        demo_software_id_generation()
        
        # 5. Analyze license format
        demo_format_analysis()
        
        # 6. Show cryptographic details
        demo_cryptographic_details()
        
        print("=" * 70)
        print("DEMONSTRATION COMPLETE")
        print("=" * 70)
        print()
        print("Usage Examples:")
        print("  • Command Line: python mikrotik_keygen.py --software-id 99FB-YFSC")
        print("  • GUI:          python mikrotik_keygen_gui.py")
        print("  • Interactive:  python quick_keygen.py")
        print()
        print("⚠️  IMPORTANT: Generated licenses are for educational purposes only!")
        print("   Real MikroTik devices require licenses signed with official keys.")
        
    except Exception as e:
        print(f"Demonstration failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
