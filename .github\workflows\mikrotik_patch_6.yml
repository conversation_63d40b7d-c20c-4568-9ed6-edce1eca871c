name: <PERSON> RouterOS 6.x

on:
  # push:
  #   branches: [ "main" ]
  # schedule:
  #   - cron: "0 0 * * *"
  workflow_dispatch:
    inputs:
      channel:
        description: 'Channel (stable, long-term)'
        required: true
        default: 'stable'
        type: choice
        options:
          - stable
          - long-term
      version:
        description: "Version ('', 6.49.17, 6.49.18,...)"
        required: false
        default: ''
        type: string
      buildtime:
        description: "Build Time"
        required: false
        default: ''
        type: string
permissions:
  contents: write

env:
  CUSTOM_LICENSE_PRIVATE_KEY: ${{ secrets.CUSTOM_LICENSE_PRIVATE_KEY }}
  CUSTOM_LICENSE_PUBLIC_KEY: ${{ secrets.CUSTOM_LICENSE_PUBLIC_KEY }}
  CUSTOM_NPK_SIGN_PRIVATE_KEY: ${{ secrets.CUSTOM_NPK_SIGN_PRIVATE_KEY }}
  CUSTOM_NPK_SIGN_PUBLIC_KEY: ${{ secrets.CUSTOM_NPK_SIGN_PUBLIC_KEY }}
  CUSTOM_CLOUD_PUBLIC_KEY: ${{ secrets.CUSTOM_CLOUD_PUBLIC_KEY }}
  MIKRO_LICENSE_PUBLIC_KEY: ${{ secrets.MIKRO_LICENSE_PUBLIC_KEY }}
  MIKRO_NPK_SIGN_PUBLIC_KEY: ${{ secrets.MIKRO_NPK_SIGN_PUBLIC_KEY }}
  MIKRO_CLOUD_PUBLIC_KEY: ${{ secrets.MIKRO_CLOUD_PUBLIC_KEY }}
  MIKRO_LICENCE_URL: ${{ secrets.MIKRO_LICENCE_URL }}
  CUSTOM_LICENCE_URL: ${{ secrets.CUSTOM_LICENCE_URL }}
  MIKRO_UPGRADE_URL: ${{ secrets.MIKRO_UPGRADE_URL }}
  CUSTOM_UPGRADE_URL: ${{ secrets.CUSTOM_UPGRADE_URL }}
  MIKRO_RENEW_URL: ${{ secrets.MIKRO_RENEW_URL }}
  CUSTOM_RENEW_URL: ${{ secrets.CUSTOM_RENEW_URL }}
  MIKRO_CLOUD_URL: ${{ secrets.MIKRO_CLOUD_URL }}
  CUSTOM_CLOUD_URL: ${{ secrets.CUSTOM_CLOUD_URL }}

jobs:
  Set_BuildTime:
    runs-on: ubuntu-22.04
    outputs:
      BUILD_TIME: ${{ steps.set_buildtime.outputs.BUILD_TIME }}
    steps:
      - name: Set build time
        id: set_buildtime
        run: |
          _BUILD_TIME=${{ github.event.inputs.buildtime }}
          if [ -z "$_BUILD_TIME" ]; then
            echo "BUILD_TIME=$(date +'%s')" >> $GITHUB_OUTPUT
          else
             echo "BUILD_TIME=$_BUILD_TIME" >> $GITHUB_OUTPUT
          fi
          
  Patch_RouterOS:
    needs: Set_BuildTime
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        channel: ${{ fromJSON(format('["{0}"]', github.event.inputs.channel || 'stable')) }}
    env:
      TZ: 'Asia/Shanghai'
      LATEST_VERSION: ""
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
        
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11' 

    - name: Get latest routeros version
      id: get_latest
      run: |
        echo $(uname -a)
        LATEST_VERSION=${{ github.event.inputs.version }}
        if [ -z  "$_LATEST_VERSION" ]; then
          LATEST_VERSION=$(wget -nv -O - https://${{ env.MIKRO_UPGRADE_URL }}/routeros/NEWEST6.${{ matrix.channel }} | cut -d ' ' -f1)
        fi
        echo Latest Version:$LATEST_VERSION
        if [ "${{ github.event_name }}" == "schedule" ]; then
          _LATEST_VERSION=$(wget -nv -O - https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/NEWEST6.${{ matrix.channel }}  | cut -d ' ' -f1)
          if [ "$_LATEST_VERSION" == "$LATEST_VERSION" ]; then
            echo "No new version found"
            echo "has_new_version=false" >> $GITHUB_OUTPUT
            exit 0
          fi
        fi
        echo "has_new_version=true" >> $GITHUB_OUTPUT
        BUILD_TIME=${{ needs.Set_BuildTime.outputs.BUILD_TIME }}
        echo Build Time:$BUILD_TIME
        wget -nv -O CHANGELOG https://${{ env.MIKRO_UPGRADE_URL }}/routeros/$LATEST_VERSION/CHANGELOG
        cat CHANGELOG
        echo "LATEST_VERSION=${LATEST_VERSION}" >> $GITHUB_ENV
        echo "BUILD_TIME=${BUILD_TIME}" >> $GITHUB_ENV
        
        has_patched_npk=false
        if wget -nv --spider https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/$LATEST_VERSION/routeros-x86-$LATEST_VERSION.npk; then
          echo "RouterOS NPK file exists, attempting to download..."
          if wget -nv -O routeros-x86-$LATEST_VERSION.npk https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/$LATEST_VERSION/routeros-x86-$LATEST_VERSION.npk; then
            echo "RouterOS NPK file downloaded successfully."
            if wget -nv --spider https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/$LATEST_VERSION/system-$LATEST_VERSION.npk; then
              echo "System NPK file exists, attempting to download..."
              if wget -nv -O system-$LATEST_VERSION.npk https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/$LATEST_VERSION/system-$LATEST_VERSION.npk; then
                has_patched_npk=true
              fi
            fi
          fi
        fi
        echo "has_patched_npk=$has_patched_npk" >> $GITHUB_OUTPUT

    - name: Cache Squashfs
      if: steps.get_latest.outputs.has_new_version == 'true'
      id: cache-squashfs
      uses: actions/cache@v4
      with:
        path: |
          option.sfs
        key: busybox-squashfs
        
    - name: Create Squashfs for option
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-squashfs.outputs.cache-hit != 'true'
      run: |
        sudo mkdir -p ./option-root/bin/
        sudo cp busybox/busybox_x86 ./option-root/bin/busybox
        sudo chmod +x ./option-root/bin/busybox
        sudo cp keygen/keygen_x86 ./option-root/bin/keygen
        sudo chmod +x ./option-root/bin/keygen

        sudo chmod +x ./busybox/busybox_x86
        COMMANDS=$(./busybox/busybox_x86 --list)
        for cmd in $COMMANDS; do
            sudo ln -sf /pckg/option/bin/busybox ./option-root/bin/$cmd
        done
        sudo mksquashfs option-root option.sfs -quiet -comp xz -no-xattrs -b 256k
        sudo rm -rf option-root
        
    - name: Cache mikrotik-${{ env.LATEST_VERSION }}.iso
      if: steps.get_latest.outputs.has_new_version == 'true'
      id: cache-mikrotik
      uses: actions/cache@v4
      with:
        path: |
          mikrotik.iso
        key: mikrotik-${{ env.LATEST_VERSION }}

    - name: Get mikrotik-${{ env.LATEST_VERSION }}.iso
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-mikrotik.outputs.cache-hit != 'true'
      run: |
          sudo wget -nv -O mikrotik.iso https://download.mikrotik.com/routeros/$LATEST_VERSION/mikrotik-$LATEST_VERSION.iso


    - name: Patch mikrotik-${{ env.LATEST_VERSION }}.iso
      if: steps.get_latest.outputs.has_new_version == 'true'
      run: |
        sudo apt-get install -y mkisofs xorriso > /dev/null
        sudo mkdir ./iso 
        sudo mount -o loop,ro mikrotik.iso ./iso
        sudo mkdir ./new_iso
        sudo cp -r ./iso/* ./new_iso/
        sudo rsync -a ./iso/ ./new_iso/
        sudo umount ./iso
        sudo rm -rf ./iso
        if [ "${{ steps.get_latest.outputs.has_patched_npk }}" == "false" ]; then
          sudo mv ./new_iso/system-$LATEST_VERSION.npk ./
          sudo -E python3 patch.py npk system-$LATEST_VERSION.npk
        else
          sudo -E python3 npk.py sign system-$LATEST_VERSION.npk system-$LATEST_VERSION.npk
        fi
        sudo -E python3 patch.py kernel ./new_iso/isolinux/initrd.rgz
        NPK_FILES=$(find ./new_iso/*.npk)
        for file in $NPK_FILES; do
            sudo -E python3 npk.py sign $file $file
        done
        sudo cp system-$LATEST_VERSION.npk ./new_iso/
        #sudo -E python3 npk.py create ./new_iso/gps-$LATEST_VERSION.npk ./option-$LATEST_VERSION.npk option ./option.sfs -desc="busybox"
        #sudo cp option-$LATEST_VERSION.npk ./new_iso/
        sudo mkisofs -o mikrotik-$LATEST_VERSION.iso \
                    -V "MikroTik $LATEST_VERSION" \
                    -sysid "" -preparer "MiKroTiK" \
                    -publisher "" -A "MiKroTiK RouterOS" \
                    -input-charset utf-8 \
                    -b isolinux/isolinux.bin \
                    -c isolinux/boot.cat \
                    -no-emul-boot \
                    -boot-load-size 4 \
                    -boot-info-table \
                    -R -J \
                    ./new_iso
   
        sudo mkdir ./all_packages
        sudo cp ./new_iso/*.npk ./all_packages/
        sudo rm -rf ./new_iso

    - name: Create install-image-${{ env.LATEST_VERSION }}.img
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.get_latest.outputs.has_patched_npk == 'true'
      run: |
        sudo modprobe nbd
        sudo apt-get update
        sudo apt-get install -y qemu-utils extlinux > /dev/null
        truncate --size 64M install-image-$LATEST_VERSION.img
        sudo qemu-nbd -c /dev/nbd0 -f raw install-image-$LATEST_VERSION.img
        sudo mkfs.vfat -n "Install" /dev/nbd0
        sudo mkdir ./install
        sudo mount /dev/nbd0 ./install
        sudo extlinux --install -H 64 -S 32 ./install/
        echo -e 'default system\nLABEL system\n\tKERNEL linux\n\tAPPEND load_ramdisk=1 initrd=initrd.rgz -hdd-install' \
          > syslinux.cfg
        sudo cp syslinux.cfg ./install/
        sudo rm syslinux.cfg
        NPK_FILES=($(find ./all_packages/*.npk))
        for ((i=1; i<=${#NPK_FILES[@]}; i++))
        do
          echo "${NPK_FILES[$i-1]}=>$i.npk" 
          sudo cp ${NPK_FILES[$i-1]} ./install/$i.npk
        done
        sudo touch ./install/CHOOSE
        sudo touch ./install/autorun.scr 
        sudo umount /dev/nbd0
        sudo qemu-nbd -d /dev/nbd0
        sudo rm -rf ./install
        sudo zip install-image-$LATEST_VERSION.zip ./install-image-$LATEST_VERSION.img
        sudo rm ./install-image-$LATEST_VERSION.img

    - name: Cache chr-${{ env.LATEST_VERSION }}.img.zip
      if: steps.get_latest.outputs.has_new_version == 'true'
      id: cache-chr-img
      uses: actions/cache@v4
      with:
        path: |
          chr.img
        key: chr-${{ env.LATEST_VERSION }}.img

    - name: Get chr-${{ env.LATEST_VERSION }}.img
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-chr-img.outputs.cache-hit != 'true'
      run: |
          sudo wget -nv -O chr.img.zip https://download.mikrotik.com/routeros/$LATEST_VERSION/chr-$LATEST_VERSION.img.zip
          sudo unzip chr.img.zip
          sudo rm chr.img.zip
          sudo mv chr-$LATEST_VERSION.img chr.img

    - name: Create chr-${{ env.LATEST_VERSION }}.img
      if: steps.get_latest.outputs.has_new_version == 'true'
      run: |
        sudo modprobe nbd
        sudo apt-get install -y qemu-utils extlinux > /dev/null
        sudo mkdir ./chr
        sudo cp chr.img chr-$LATEST_VERSION.img
        sudo qemu-nbd -c /dev/nbd0 -f raw chr-$LATEST_VERSION.img
        sudo -E python3 patch.py block /dev/nbd0p1 boot/initrd.rgz
        sudo mount /dev/nbd0p1 ./chr
        sudo cp ./all_packages/dude-$LATEST_VERSION.npk ./chr/var/pdb/dude/image
        if [ "${{ steps.get_latest.outputs.has_patched_npk }}" == "false" ]; then
          sudo -E python3 patch.py npk ./chr/var/pdb/routeros-x86/image
          sudo cp ./chr/var/pdb/routeros-x86/image ./all_packages/routeros-x86-$LATEST_VERSION.npk
        else
          sudo -E python3 npk.py sign routeros-x86-$LATEST_VERSION.npk routeros-x86-$LATEST_VERSION.npk
          sudo cp -f routeros-x86-$LATEST_VERSION.npk ./chr/var/pdb/routeros-x86/image
          sudo cp routeros-x86-$LATEST_VERSION.npk ./all_packages/routeros-x86-$LATEST_VERSION.npk
        fi
        #sudo mkdir -p ./chr/var/pdb/option
        #sudo cp ./all_packages/option-$LATEST_VERSION.npk ./chr/var/pdb/option/image
        sudo umount ./chr
        sudo qemu-nbd -d /dev/nbd0
        sudo rm -rf ./chr
        sudo qemu-img convert -f raw -O qcow2 chr-$LATEST_VERSION.img chr-$LATEST_VERSION.qcow2
        sudo qemu-img convert -f raw -O vmdk chr-$LATEST_VERSION.img chr-$LATEST_VERSION.vmdk
        sudo qemu-img convert -f raw -O vpc chr-$LATEST_VERSION.img chr-$LATEST_VERSION.vhd
        sudo qemu-img convert -f raw -O vhdx chr-$LATEST_VERSION.img chr-$LATEST_VERSION.vhdx
        sudo qemu-img convert -f raw -O vdi chr-$LATEST_VERSION.img chr-$LATEST_VERSION.vdi

        sudo zip chr-$LATEST_VERSION.qcow2.zip chr-$LATEST_VERSION.qcow2
        sudo zip chr-$LATEST_VERSION.vmdk.zip chr-$LATEST_VERSION.vmdk
        sudo zip chr-$LATEST_VERSION.vhd.zip chr-$LATEST_VERSION.vhd
        sudo zip chr-$LATEST_VERSION.vhdx.zip chr-$LATEST_VERSION.vhdx
        sudo zip chr-$LATEST_VERSION.vdi.zip chr-$LATEST_VERSION.vdi
        sudo zip chr-$LATEST_VERSION.img.zip chr-$LATEST_VERSION.img

        sudo rm chr-$LATEST_VERSION.qcow2
        sudo rm chr-$LATEST_VERSION.vmdk
        sudo rm chr-$LATEST_VERSION.vhd
        sudo rm chr-$LATEST_VERSION.vhdx
        sudo rm chr-$LATEST_VERSION.vdi
        sudo rm chr-$LATEST_VERSION.img

        cd ./all_packages
        sudo zip ../all_packages-x86-$LATEST_VERSION.zip *.npk
        cd ..
        
    - name: Upload Files
      if: steps.get_latest.outputs.has_new_version == 'true'
      run: |
        if [ "${{ steps.get_latest.outputs.has_patched_npk }}" == "false" ]; then
          OUTDIR="$LATEST_VERSION-UNDONE"
        else
          OUTDIR="$LATEST_VERSION"
        fi
        sudo mkdir -p ./publish/$OUTDIR
        sudo cp CHANGELOG ./publish/$OUTDIR/
        sudo cp ./all_packages/*.npk ./publish/$OUTDIR/
        sudo chown -R root:root ./publish/
        sudo apt-get install -y lftp ssh sshpass > /dev/null 2>&1
        sudo -E lftp -u ${{ secrets.SSH_USERNAME }},'${{ secrets.SSH_PASSWORD }}' sftp://${{ secrets.SSH_SERVER }}:${{ secrets.SSH_PORT }} <<EOF
        set sftp:auto-confirm yes
        mirror --reverse --verbose --only-newer ./publish ${{ secrets.SSH_DIRECTORY }}
        bye
        EOF
        sshpass -p "${{ secrets.SSH_PASSWORD }}" ssh -o StrictHostKeyChecking=no -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_SERVER }} "echo $LATEST_VERSION $BUILD_TIME | tee /rw/disk/${{ secrets.SSH_DIRECTORY }}/NEWEST6.${{ matrix.channel }}; chown -R 32768:32768 /rw/disk/${{ secrets.SSH_DIRECTORY }}/"   
        
    - name: Clear Cloudflare cache
      if: steps.get_latest.outputs.has_new_version == 'true'
      run: |
        curl --request POST --url "https://api.cloudflare.com/client/v4/zones/fe6831ed6dc9e6235e69ef2a31f2e7fe/purge_cache" \
            --header "Authorization: Bearer 9GDQkzU51QXaqzp1qMjyFKpyeJyOdnNoG9GZQaGP" \
            --header "Content-Type:application/json" \
            --data '{"purge_everything": true}'

    - name: Delete Release tag ${{ env.LATEST_VERSION }}
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.get_latest.outputs.has_patched_npk == 'true'
      run: |
        HEADER="Authorization: token ${{ secrets.GITHUB_TOKEN }}"
        RELEASE_INFO=$(curl -s -H $HEADER https://api.github.com/repos/${{ github.repository }}/releases/tags/$LATEST_VERSION)
        RELEASE_ID=$(echo $RELEASE_INFO | jq -r '.id')
        echo "Release ID: $RELEASE_ID"
        if [ "$RELEASE_ID" != "null" ]; then
            curl -X DELETE -H "$HEADER" https://api.github.com/repos/${{ github.repository }}/git/refs/tags/$LATEST_VERSION
            echo "Tag $LATEST_VERSION deleted successfully."
            curl -X DELETE -H "$HEADER" https://api.github.com/repos/${{ github.repository }}/releases/$RELEASE_ID
            echo "Release with tag $LATEST_VERSION deleted successfully."
        else
            echo "Release not found for tag: $LATEST_VERSION)"
        fi

    - name: Create Release tag ${{ env.LATEST_VERSION }}
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.get_latest.outputs.has_patched_npk == 'true'
      uses: softprops/action-gh-release@v2
      with:
        name: "RouterOS ${{ env.LATEST_VERSION }}"
        body_path: "CHANGELOG"
        tag_name: ${{ env.LATEST_VERSION }}
        make_latest:  false
        prerelease:  ${{ matrix.channel == 'testing' }}
        files: |
          mikrotik-${{ env.LATEST_VERSION }}.iso
          chr-${{ env.LATEST_VERSION }}*.zip
          install-image-${{ env.LATEST_VERSION }}.zip
          routeros-x86-${{ env.LATEST_VERSION }}.npk
          all_packages-x86-${{ env.LATEST_VERSION }}.zip
