#!/bin/bash

# Custom RouterOS Build Setup Script
# Sets up environment for building custom RouterOS with your own keys and email

set -e

echo "=========================================="
echo "Custom RouterOS Build Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CUSTOM_EMAIL="<EMAIL>"
ORIGINAL_EMAIL="<EMAIL>"
ROUTEROS_VERSION="7.19.4"

echo -e "${BLUE}Setting up custom RouterOS build environment...${NC}"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}This script should not be run as root${NC}"
   exit 1
fi

# Install required packages
echo -e "${YELLOW}Installing required packages...${NC}"
sudo apt-get update
sudo apt-get install -y \
    python3 \
    python3-pip \
    wget \
    curl \
    mkisofs \
    xorriso \
    squashfs-tools \
    unsquashfs \
    qemu-utils \
    extlinux \
    syslinux \
    genisoimage \
    mtools \
    dosfstools \
    e2fsprogs \
    gdisk \
    rsync \
    lzma \
    xz-utils

# Install Python packages
echo -e "${YELLOW}Installing Python packages...${NC}"
pip3 install cryptography

# Generate custom keys
echo -e "${YELLOW}Generating custom cryptographic keys...${NC}"
python3 mikrotik_keygen.py --generate-keys > custom_keys.txt

# Extract keys from output
LICENSE_PRIVATE_KEY=$(grep "Private Key:" custom_keys.txt | cut -d' ' -f3)
LICENSE_PUBLIC_KEY=$(grep "Public Key:" custom_keys.txt | cut -d' ' -f3)

# Generate NPK signing keys (EdDSA)
python3 generate.py > npk_keys.txt
NPK_PRIVATE_KEY=$(grep "CUSTOM_LICENSE_PRIVATE_KEY" npk_keys.txt | cut -d'=' -f2 | tr -d ' ')
NPK_PUBLIC_KEY=$(grep "CUSTOM_LICENSE_PUBLIC_KEY" npk_keys.txt | cut -d'=' -f2 | tr -d ' ')

echo -e "${GREEN}Keys generated successfully!${NC}"
echo "License Private Key: ${LICENSE_PRIVATE_KEY:0:16}..."
echo "License Public Key:  ${LICENSE_PUBLIC_KEY:0:16}..."
echo "NPK Private Key:     ${NPK_PRIVATE_KEY:0:16}..."
echo "NPK Public Key:      ${NPK_PUBLIC_KEY:0:16}..."

# Create environment file
echo -e "${YELLOW}Creating environment configuration...${NC}"
cat > custom_build_env.sh << EOF
#!/bin/bash
# Custom RouterOS Build Environment Variables

# Custom Keys
export CUSTOM_LICENSE_PRIVATE_KEY="$LICENSE_PRIVATE_KEY"
export CUSTOM_LICENSE_PUBLIC_KEY="$LICENSE_PUBLIC_KEY"
export CUSTOM_NPK_SIGN_PRIVATE_KEY="$NPK_PRIVATE_KEY"
export CUSTOM_NPK_SIGN_PUBLIC_KEY="$NPK_PUBLIC_KEY"
export CUSTOM_CLOUD_PUBLIC_KEY="$NPK_PUBLIC_KEY"

# Original MikroTik Keys (you need to obtain these)
export MIKRO_LICENSE_PUBLIC_KEY="original_mikrotik_license_public_key_here"
export MIKRO_NPK_SIGN_PUBLIC_KEY="original_mikrotik_npk_public_key_here"
export MIKRO_CLOUD_PUBLIC_KEY="original_mikrotik_cloud_public_key_here"

# URLs - replace with your own servers
export MIKRO_LICENCE_URL="licence.mikrotik.com"
export CUSTOM_LICENCE_URL="your-license-server.com"
export MIKRO_UPGRADE_URL="upgrade.mikrotik.com"
export CUSTOM_UPGRADE_URL="your-upgrade-server.com"
export MIKRO_RENEW_URL="renew.mikrotik.com"
export CUSTOM_RENEW_URL="your-renew-server.com"
export MIKRO_CLOUD_URL="cloud.mikrotik.com"
export CUSTOM_CLOUD_URL="your-cloud-server.com"

# Custom settings
export CUSTOM_EMAIL="$CUSTOM_EMAIL"
export ORIGINAL_EMAIL="$ORIGINAL_EMAIL"
export ROUTEROS_VERSION="$ROUTEROS_VERSION"

echo "Custom RouterOS build environment loaded!"
EOF

chmod +x custom_build_env.sh

# Create enhanced patch script with email replacement
echo -e "${YELLOW}Creating enhanced patch script...${NC}"
cat > patch_custom.py << 'EOF'
#!/usr/bin/env python3
"""
Enhanced patch script with email replacement
"""

import os
import sys
import subprocess
import re
from patch import *

def replace_email_in_squashfs(path, old_email, new_email):
    """Replace email addresses in squashfs files"""
    print(f"Replacing {old_email} with {new_email} in {path}")
    
    for root, dirs, files in os.walk(path):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.isfile(file_path):
                try:
                    # Read file content
                    with open(file_path, 'rb') as f:
                        content = f.read()
                    
                    # Replace email if found
                    if old_email.encode() in content:
                        print(f"Found email in {file_path}, replacing...")
                        new_content = content.replace(
                            old_email.encode(),
                            new_email.encode()
                        )
                        with open(file_path, 'wb') as f:
                            f.write(new_content)
                            
                except (UnicodeDecodeError, PermissionError, OSError):
                    # Skip binary files or files we can't read
                    continue

# Override the patch_squashfs function to include email replacement
original_patch_squashfs = patch_squashfs

def enhanced_patch_squashfs(path, key_dict):
    """Enhanced squashfs patching with email replacement"""
    # Call original patching
    original_patch_squashfs(path, key_dict)
    
    # Add email replacement
    old_email = os.environ.get('ORIGINAL_EMAIL', '<EMAIL>')
    new_email = os.environ.get('CUSTOM_EMAIL', '<EMAIL>')
    
    replace_email_in_squashfs(path, old_email, new_email)
    
    # Update logo file with custom email and info
    logo = os.path.join(path, "nova/lib/console/logo.txt")
    if os.path.exists(logo):
        print(f"Updating logo file: {logo}")
        run_shell_command(f"sudo sed -i '1d' {logo}")
        run_shell_command(f"sudo sed -i '8s#.*#  {new_email}     https://github.com/your-repo/CustomMikroTikPatch#' {logo}")

# Replace the function
patch_squashfs = enhanced_patch_squashfs

if __name__ == '__main__':
    # Run the original patch.py main function
    import patch
    patch.main()
EOF

chmod +x patch_custom.py

# Create build script
echo -e "${YELLOW}Creating build script...${NC}"
cat > build_custom_routeros.sh << 'EOF'
#!/bin/bash

# Load environment
source ./custom_build_env.sh

VERSION=${1:-$ROUTEROS_VERSION}
ARCH=${2:-"x86"}

echo "Building custom RouterOS $VERSION for $ARCH"

# Set architecture
export ARCH=$ARCH

# Download RouterOS
if [ "$ARCH" = "x86" ]; then
    ISO_URL="https://download.mikrotik.com/routeros/$VERSION/mikrotik-$VERSION.iso"
    ISO_FILE="mikrotik-$VERSION.iso"
elif [ "$ARCH" = "arm64" ]; then
    ISO_URL="https://download.mikrotik.com/routeros/$VERSION/mikrotik-$VERSION-arm64.iso"
    ISO_FILE="mikrotik-$VERSION-arm64.iso"
else
    echo "Unsupported architecture: $ARCH"
    exit 1
fi

echo "Downloading $ISO_FILE..."
wget -O $ISO_FILE $ISO_URL

# Mount and extract ISO
sudo mkdir -p ./iso
sudo mount -o loop,ro $ISO_FILE ./iso
sudo mkdir -p ./new_iso
sudo cp -r ./iso/* ./new_iso/
sudo rsync -a ./iso/ ./new_iso/
sudo umount ./iso
sudo rm -rf ./iso

# Patch system package
SYSTEM_NPK="./new_iso/system-$VERSION.npk"
if [ -f "$SYSTEM_NPK" ]; then
    sudo mv $SYSTEM_NPK ./
    sudo -E python3 patch_custom.py npk system-$VERSION.npk
    sudo cp system-$VERSION.npk ./new_iso/
fi

# Patch kernel/bootloader
if [ "$ARCH" = "x86" ]; then
    sudo -E python3 patch_custom.py kernel ./new_iso/isolinux/initrd.rgz
elif [ "$ARCH" = "arm64" ]; then
    if [ -f "./new_iso/EFI/BOOT/BOOTAA64.EFI" ]; then
        sudo -E python3 patch_custom.py kernel ./new_iso/EFI/BOOT/BOOTAA64.EFI
    fi
fi

# Sign all NPK files
find ./new_iso/ -name "*.npk" | while read npk_file; do
    if [ -f "$npk_file" ]; then
        echo "Signing $npk_file"
        sudo -E python3 npk.py sign "$npk_file" "$npk_file"
    fi
done

# Create custom ISO
CUSTOM_ISO="custom-mikrotik-$VERSION-$ARCH.iso"

if [ "$ARCH" = "x86" ]; then
    sudo mkisofs -o $CUSTOM_ISO \
        -V "Custom MikroTik $VERSION" \
        -sysid "" -preparer "Custom MikroTik" \
        -publisher "" -A "Custom MikroTik RouterOS" \
        -input-charset utf-8 \
        -b isolinux/isolinux.bin \
        -c isolinux/boot.cat \
        -no-emul-boot \
        -boot-load-size 4 \
        -boot-info-table \
        -R -J \
        ./new_iso
elif [ "$ARCH" = "arm64" ]; then
    sudo xorriso -as mkisofs -o $CUSTOM_ISO \
        -V "Custom MikroTik $VERSION ARM64" \
        -sysid "" -preparer "Custom MikroTik" \
        -publisher "" -A "Custom MikroTik RouterOS" \
        -input-charset utf-8 \
        -b efiboot.img \
        -no-emul-boot \
        -R -J \
        ./new_iso
fi

# Cleanup
sudo rm -rf ./new_iso

echo "Custom $ARCH ISO created: $CUSTOM_ISO"
echo "Build completed successfully!"
EOF

chmod +x build_custom_routeros.sh

# Create quick build script for both architectures
cat > build_all.sh << 'EOF'
#!/bin/bash

echo "Building custom RouterOS for all architectures..."

# Build x86
echo "Building x86 version..."
./build_custom_routeros.sh $1 x86

# Build ARM64
echo "Building ARM64 version..."
./build_custom_routeros.sh $1 arm64

echo "All builds completed!"
EOF

chmod +x build_all.sh

# Create license generator script
cat > generate_license.sh << 'EOF'
#!/bin/bash

# Load environment
source ./custom_build_env.sh

SOFTWARE_ID=${1:-"CUST-OM01"}
VERSION=${2:-7}
LEVEL=${3:-4}

echo "Generating license for Software ID: $SOFTWARE_ID"

python3 mikrotik_keygen.py \
    --software-id $SOFTWARE_ID \
    --version $VERSION \
    --level $LEVEL \
    --private-key $CUSTOM_LICENSE_PRIVATE_KEY

echo ""
echo "License generated with your custom private key!"
echo "This license will work with your custom RouterOS build."
EOF

chmod +x generate_license.sh

# Cleanup temporary files
rm -f custom_keys.txt npk_keys.txt

echo -e "${GREEN}Setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}Files created:${NC}"
echo "  - custom_build_env.sh     (Environment variables)"
echo "  - patch_custom.py         (Enhanced patch script)"
echo "  - build_custom_routeros.sh (Build script)"
echo "  - build_all.sh            (Build all architectures)"
echo "  - generate_license.sh     (License generator)"
echo ""
echo -e "${BLUE}Usage:${NC}"
echo "  1. Build x86 ISO:     ./build_custom_routeros.sh 7.19.4 x86"
echo "  2. Build ARM64 ISO:   ./build_custom_routeros.sh 7.19.4 arm64"
echo "  3. Build both:        ./build_all.sh 7.19.4"
echo "  4. Generate license:  ./generate_license.sh CUST-OM01 7 4"
echo ""
echo -e "${YELLOW}Important Notes:${NC}"
echo "  - Your custom email: $CUSTOM_EMAIL"
echo "  - Original email will be replaced in all files"
echo "  - Custom keys have been generated and saved"
echo "  - You need to obtain original MikroTik public keys"
echo "  - Update URLs in custom_build_env.sh to point to your servers"
echo ""
echo -e "${GREEN}Ready to build custom RouterOS!${NC}"
