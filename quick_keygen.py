#!/usr/bin/env python3
"""
Quick MikroTik License Key Generator
Simple command-line utility for generating MikroTik license keys.
"""

import sys
import random
from mikrotik_keygen import MikroTikLicenseGenerator

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("    MikroTik License Key Generator")
    print("    Quick Command-Line Version")
    print("=" * 60)
    print()

def generate_sample_license():
    """Generate a sample license with random parameters"""
    generator = MikroTikLicenseGenerator()
    
    # Generate random software ID
    software_id = generator.generate_software_id()
    
    # Random version and level
    version = random.choice([6, 7])
    level = random.randint(1, 6)
    
    print(f"Generating license with:")
    print(f"  Software ID: {software_id}")
    print(f"  Version: {version}")
    print(f"  Level: {level}")
    print()
    
    # Generate license
    license_key = generator.create_license_key(software_id, version, level)
    
    print("Generated License Key:")
    print("-" * 50)
    print(license_key)
    print("-" * 50)
    print()
    print("Note: This license was generated with a random private key.")
    print("It's for demonstration purposes only and won't work with real MikroTik devices.")
    print("To generate valid licenses, you need the proper private key.")

def generate_custom_license():
    """Generate a license with custom parameters"""
    print("Enter license parameters (press Enter for default/random):")
    print()
    
    # Get software ID
    software_id = input("Software ID (e.g., 99FB-YFSC): ").strip()
    if not software_id:
        generator = MikroTikLicenseGenerator()
        software_id = generator.generate_software_id()
        print(f"Generated random Software ID: {software_id}")
    
    # Get version
    version_input = input("Version (6 or 7, default 7): ").strip()
    try:
        version = int(version_input) if version_input else 7
        if version not in [6, 7]:
            version = 7
    except ValueError:
        version = 7
    
    # Get level
    level_input = input("Level (1-6, default 4): ").strip()
    try:
        level = int(level_input) if level_input else 4
        if level < 1 or level > 6:
            level = 4
    except ValueError:
        level = 4
    
    # Get private key
    private_key = input("Private key (hex, leave empty for random): ").strip()
    if not private_key:
        private_key = None
        print("Using random private key (for testing only)")
    
    print()
    print(f"Generating license with:")
    print(f"  Software ID: {software_id}")
    print(f"  Version: {version}")
    print(f"  Level: {level}")
    print()
    
    # Generate license
    generator = MikroTikLicenseGenerator(private_key)
    license_key = generator.create_license_key(software_id, version, level)
    
    print("Generated License Key:")
    print("-" * 50)
    print(license_key)
    print("-" * 50)
    print()
    
    if private_key:
        print("License generated with provided private key.")
    else:
        print("Note: This license was generated with a random private key.")
        print("It's for demonstration purposes only.")

def verify_license():
    """Verify a license key"""
    print("License Verification")
    print("-" * 20)
    print()
    
    # Get license content
    print("Enter the license key (paste the entire license including headers):")
    print("Press Ctrl+D (Linux/Mac) or Ctrl+Z (Windows) when done:")
    print()
    
    license_lines = []
    try:
        while True:
            line = input()
            license_lines.append(line)
    except EOFError:
        pass
    
    license_content = '\n'.join(license_lines)
    
    if not license_content.strip():
        print("No license content provided.")
        return
    
    # Get public key
    public_key = input("\nEnter public key (hex): ").strip()
    if not public_key:
        print("No public key provided.")
        return
    
    # Verify license
    generator = MikroTikLicenseGenerator()
    try:
        is_valid = generator.verify_license(license_content, public_key)
        print()
        print("Verification Result:")
        print("-" * 20)
        if is_valid:
            print("✓ License is VALID")
        else:
            print("✗ License is INVALID")
    except Exception as e:
        print(f"Verification failed: {e}")

def show_help():
    """Show help information"""
    print("MikroTik License Key Generator - Help")
    print("=" * 40)
    print()
    print("This tool generates MikroTik license keys for educational purposes.")
    print()
    print("Available options:")
    print("  1. Generate sample license - Creates a license with random parameters")
    print("  2. Generate custom license - Allows you to specify parameters")
    print("  3. Verify license - Verifies an existing license key")
    print("  4. Help - Shows this help message")
    print("  5. Exit - Exits the application")
    print()
    print("License Format:")
    print("  Software ID: 4-character identifier (e.g., 99FB-YFSC)")
    print("  Version: RouterOS version (6 or 7)")
    print("  Level: License level (1-6, where 6 is unlimited)")
    print()
    print("Note: Generated licenses are for demonstration only unless you")
    print("      have the proper private key for signing.")

def main():
    """Main application loop"""
    print_banner()
    
    while True:
        print("Select an option:")
        print("  1. Generate sample license")
        print("  2. Generate custom license")
        print("  3. Verify license")
        print("  4. Help")
        print("  5. Exit")
        print()
        
        choice = input("Enter your choice (1-5): ").strip()
        print()
        
        if choice == '1':
            generate_sample_license()
        elif choice == '2':
            generate_custom_license()
        elif choice == '3':
            verify_license()
        elif choice == '4':
            show_help()
        elif choice == '5':
            print("Thank you for using MikroTik License Key Generator!")
            break
        else:
            print("Invalid choice. Please enter 1-5.")
        
        print()
        input("Press Enter to continue...")
        print()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        sys.exit(0)
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)
