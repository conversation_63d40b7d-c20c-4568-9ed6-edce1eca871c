name: <PERSON> RouterOS 7.x
on:
  # push:
  #   branches: [ "main" ]
  schedule:
    - cron: "0 0 * * *" # At UTC 00:00 on every day
  workflow_dispatch:
    inputs:
      arch:
        description: 'Architecture (x86, arm64, arm)'
        required: true
        default: 'x86'
        type: choice
        options:
          - x86
          - arm64
          - arm
      channel:
        description: 'Channel (stable, testing)'
        required: true
        default: 'stable'
        type: choice
        options:
          - stable
          - testing
      version:
        description: "Specify version (e.g., 7.19.2), empty for latest"
        required: false
        default: ''
        type: string
      buildtime:
        description: "Custom build time, leave empty for now"
        required: false
        default: ''
        type: string
      release:
        description: "Release to GitHub & Upload "
        required: false
        default: false
        type: boolean

permissions:
  contents: write

env:
  CUSTOM_LICENSE_PRIVATE_KEY: ${{ secrets.CUSTOM_LICENSE_PRIVATE_KEY }}
  CUSTOM_LICENSE_PUBLIC_KEY: ${{ secrets.CUSTOM_LICENSE_PUBLIC_KEY }}
  CUSTOM_NPK_SIGN_PRIVATE_KEY: ${{ secrets.CUSTOM_NPK_SIGN_PRIVATE_KEY }}
  CUSTOM_NPK_SIGN_PUBLIC_KEY: ${{ secrets.CUSTOM_NPK_SIGN_PUBLIC_KEY }}
  CUSTOM_CLOUD_PUBLIC_KEY: ${{ secrets.CUSTOM_CLOUD_PUBLIC_KEY }}
  MIKRO_LICENSE_PUBLIC_KEY: ${{ secrets.MIKRO_LICENSE_PUBLIC_KEY }}
  MIKRO_NPK_SIGN_PUBLIC_KEY: ${{ secrets.MIKRO_NPK_SIGN_PUBLIC_KEY }}
  MIKRO_CLOUD_PUBLIC_KEY: ${{ secrets.MIKRO_CLOUD_PUBLIC_KEY }}
  MIKRO_LICENCE_URL: ${{ secrets.MIKRO_LICENCE_URL }}
  CUSTOM_LICENCE_URL: ${{ secrets.CUSTOM_LICENCE_URL }}
  MIKRO_UPGRADE_URL: ${{ secrets.MIKRO_UPGRADE_URL }}
  CUSTOM_UPGRADE_URL: ${{ secrets.CUSTOM_UPGRADE_URL }}
  MIKRO_RENEW_URL: ${{ secrets.MIKRO_RENEW_URL }}
  CUSTOM_RENEW_URL: ${{ secrets.CUSTOM_RENEW_URL }}
  MIKRO_CLOUD_URL: ${{ secrets.MIKRO_CLOUD_URL }}
  CUSTOM_CLOUD_URL: ${{ secrets.CUSTOM_CLOUD_URL }}

jobs:
  Set_Variables:
    runs-on: ubuntu-22.04
    outputs:
      BUILD_TIME: ${{ steps.set_vars.outputs.BUILD_TIME }}
      RELEASE: ${{ steps.set_vars.outputs.RELEASE }}
      MATRIX_JSON: ${{ steps.set_vars.outputs.MATRIX_JSON }}
    steps:
      - name: Set variables
        id: set_vars
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            BUILD_TIME="${{ github.event.inputs.buildtime }}"
            RELEASE="${{ github.event.inputs.release }}"
            if [ -z "$BUILD_TIME" ]; then
              BUILD_TIME=$(date +'%s')
            fi
            if [ -z "$RELEASE" ]; then
              RELEASE=false
            fi
            ARCH="${{ github.event.inputs.arch }}"
            CHANNEL="${{ github.event.inputs.channel }}"
            MATRIX_JSON=$(jq -nc --arg arch "$ARCH" --arg channel "$CHANNEL" '[{arch: $arch, channel: $channel}]')
          else
            BUILD_TIME=$(date +'%s')
            RELEASE=true
            MATRIX_JSON='[{"arch":"x86","channel":"stable"},{"arch":"arm64","channel":"stable"},{"arch":"arm","channel":"stable"},{"arch":"x86","channel":"testing"},{"arch":"arm64","channel":"testing"},{"arch":"arm","channel":"testing"}]'
            MATRIX_JSON='[{"arch":"x86","channel":"stable"},{"arch":"arm64","channel":"stable"},{"arch":"x86","channel":"testing"},{"arch":"arm64","channel":"testing"}]'
          fi
          echo "BUILD_TIME=$BUILD_TIME" >> $GITHUB_OUTPUT
          echo "RELEASE=$RELEASE" >> $GITHUB_OUTPUT
          echo "MATRIX_JSON=$MATRIX_JSON" >> $GITHUB_OUTPUT
          echo "BUILD_TIME: $BUILD_TIME"
          echo "RELEASE: $RELEASE"
          echo "MATRIX_JSON: $MATRIX_JSON"

  Patch_RouterOS:
    needs: Set_Variables
    runs-on: ubuntu-22.04
    strategy:
      matrix: 
        include: ${{ fromJSON(needs.Set_Variables.outputs.MATRIX_JSON) }}
    env:
      TZ: 'Asia/Shanghai'
      BUILD_TIME: ${{ needs.Set_Variables.outputs.BUILD_TIME }}
      RELEASE: ${{ needs.Set_Variables.outputs.RELEASE }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
        
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11' 

    - name: Get latest routeros version
      id: get_latest
      run: |
        echo $(uname -a)
        echo Build Time:$BUILD_TIME
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          if [ -n "${{ github.event.inputs.version }}" ]; then
            LATEST_VERSION="${{ github.event.inputs.version }}"
          else
            LATEST_VERSION=$(wget -nv -O - https://${{ env.MIKRO_UPGRADE_URL }}/routeros/NEWESTa7.${{ matrix.channel }} | cut -d ' ' -f1)
          fi
        else
          LATEST_VERSION=$(wget -nv -O - https://${{ env.MIKRO_UPGRADE_URL }}/routeros/NEWESTa7.${{ matrix.channel }} | cut -d ' ' -f1)
        fi
        echo Latest Version:$LATEST_VERSION
        if [ "${{ github.event_name }}" == "schedule" ]; then
          _LATEST_VERSION=$(wget -nv -O - https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/NEWESTa7.${{ matrix.channel }} | cut -d ' ' -f1)
          if [ "$_LATEST_VERSION" == "$LATEST_VERSION" ]; then
            echo "No new version found"
            echo "has_new_version=false" >> $GITHUB_OUTPUT
            exit 0
          fi
        fi
        echo "has_new_version=true" >> $GITHUB_OUTPUT
        wget -nv -O CHANGELOG https://${{ env.MIKRO_UPGRADE_URL }}/routeros/$LATEST_VERSION/CHANGELOG
        cat CHANGELOG
        if [[ "${{ matrix.arch }}" == "arm64" ]]; then
          echo "ARCH=-arm64" >> $GITHUB_ENV
        elif [[ "${{ matrix.arch }}" == "arm" ]]; then
          echo "ARCH=-arm" >> $GITHUB_ENV
        else
          echo "ARCH=" >> $GITHUB_ENV
        fi
        echo "LATEST_VERSION=${LATEST_VERSION}" >> $GITHUB_ENV
        sudo apt-get update > /dev/null
        
    - name: Get loader patch files
      if: steps.get_latest.outputs.has_new_version == 'true'
      run: |
        wget -nv -O loader.7z https://${{ env.CUSTOM_UPGRADE_URL }}/routeros/loader.7z
        sudo apt-get install -y p7zip-full > /dev/null
        7z x -p"${{ secrets.LOADER_7Z_PASSWORD }}" loader.7z >> /dev/null
        rm loader.7z

    - name: Cache Squashfs
      if: steps.get_latest.outputs.has_new_version == 'true'
      id: cache-squashfs
      uses: actions/cache@v4
      with:
        path: |
          python3.sfs
          option.sfs
        key: busybox-python3-squashfs-${{ matrix.arch }}

    - name: Create Squashfs for option and python3
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-squashfs.outputs.cache-hit != 'true'
      run: |
        sudo mkdir -p ./option-root/bin/
        if [ "${{ matrix.arch }}" == "x86" ]; then
          sudo cp busybox/busybox_x86 ./option-root/bin/busybox
          sudo chmod +x ./option-root/bin/busybox
          sudo cp keygen/keygen_x86 ./option-root/bin/keygen
          sudo chmod +x ./option-root/bin/keygen
        elif [ "${{ matrix.arch }}" == "arm64" ]; then
          sudo cp busybox/busybox_aarch64 ./option-root/bin/busybox
          sudo chmod +x ./option-root/bin/busybox
          sudo cp keygen/keygen_aarch64 ./option-root/bin/keygen
          sudo chmod +x ./option-root/bin/keygen
        elif [ "${{ matrix.arch }}" == "arm" ]; then
          sudo cp busybox/busybox_arm ./option-root/bin/busybox
          sudo chmod +x ./option-root/bin/busybox
          sudo cp keygen/keygen_arm ./option-root/bin/keygen
          sudo chmod +x ./option-root/bin/keygen
        else
          echo "Unsupported architecture: ${{ matrix.arch }}"
          exit 1
        fi
        sudo chmod +x ./busybox/busybox_x86
        COMMANDS=$(./busybox/busybox_x86 --list)
        for cmd in $COMMANDS; do
            sudo ln -sf /pckg/option/bin/busybox ./option-root/bin/$cmd
        done
        sudo mksquashfs option-root option.sfs -quiet -comp xz -no-xattrs -b 256k
        sudo rm -rf option-root
        if [ "${{ matrix.arch }}" == "x86" ]; then
          sudo wget -O cpython.tar.gz -nv https://github.com/indygreg/python-build-standalone/releases/download/20250708/cpython-3.11.13+20250708-x86_64-unknown-linux-musl-install_only_stripped.tar.gz
        elif [ "${{ matrix.arch }}" == "arm64" ]; then
          sudo wget -O cpython.tar.gz -nv https://github.com/indygreg/python-build-standalone/releases/download/20250708/cpython-3.11.13+20250708-aarch64-unknown-linux-gnu-install_only_stripped.tar.gz
        elif [ "${{ matrix.arch }}" == "arm" ]; then
          sudo wget -O cpython.tar.gz -nv https://github.com/indygreg/python-build-standalone/releases/download/20250708/cpython-3.11.13+20250708-armv7-unknown-linux-gnueabi-install_only_stripped.tar.gz
        fi
        sudo tar -xf cpython.tar.gz
        sudo rm cpython.tar.gz
        sudo rm -rf ./python/include
        sudo rm -rf ./python/share
        sudo mksquashfs python python3.sfs -quiet -comp xz -no-xattrs -b 256k
        sudo rm -rf ./python

    - name: Cache mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}.iso
      if: steps.get_latest.outputs.has_new_version == 'true'  && (matrix.arch == 'x86' || matrix.arch == 'arm64')
      id: cache-mikrotik
      uses: actions/cache@v4
      with:
        path: |
          mikrotik.iso
        key: mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}

    - name: Get mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}.iso
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-mikrotik.outputs.cache-hit != 'true' && (matrix.arch == 'x86' || matrix.arch == 'arm64')
      run: |
          sudo wget -nv -O mikrotik.iso https://download.mikrotik.com/routeros/$LATEST_VERSION/mikrotik-$LATEST_VERSION$ARCH.iso


    - name: Patch mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}.iso
      if: steps.get_latest.outputs.has_new_version == 'true' && (matrix.arch == 'x86' || matrix.arch == 'arm64')
      run: |
        sudo apt-get install -y mkisofs xorriso > /dev/null
        sudo mkdir ./iso 
        sudo mount -o loop,ro mikrotik.iso ./iso
        sudo mkdir ./new_iso
        sudo rsync -a ./iso/ ./new_iso/
        sudo umount ./iso
        sudo rm -rf ./iso
        sudo mv ./new_iso/routeros-$LATEST_VERSION$ARCH.npk ./
        sudo -E python3 patch.py npk routeros-$LATEST_VERSION$ARCH.npk
        NPK_FILES=$(find ./new_iso/*.npk)
        for file in $NPK_FILES; do
            sudo -E python3 npk.py sign $file $file
        done
        sudo cp -f routeros-$LATEST_VERSION$ARCH.npk ./new_iso/
        sudo -E python3 npk.py create ./new_iso/gps-$LATEST_VERSION$ARCH.npk ./option-$LATEST_VERSION$ARCH.npk option ./option.sfs -desc="busybox"
        sudo cp option-$LATEST_VERSION$ARCH.npk ./new_iso/
        sudo -E python3 npk.py create ./new_iso/gps-$LATEST_VERSION$ARCH.npk ./python3-$LATEST_VERSION$ARCH.npk python3 ./python3.sfs -desc="python 3.11.13"
        sudo cp python3-$LATEST_VERSION$ARCH.npk ./new_iso/
        sudo mkdir ./efiboot
        sudo mount -o loop ./new_iso/efiboot.img ./efiboot
        if [ "${{ matrix.arch }}" == "x86" ]; then
          sudo -E python3 patch.py kernel ./efiboot/linux.x86_64
          sudo cp ./efiboot/linux.x86_64 ./BOOTX64.EFI
          sudo cp ./BOOTX64.EFI ./new_iso/isolinux/linux
          sudo umount ./efiboot
          sudo mkisofs -o mikrotik-$LATEST_VERSION$ARCH.iso \
                      -V "MikroTik $LATEST_VERSION ${{ matrix.arch }}" \
                      -sysid "" -preparer "MiKroTiK" \
                      -publisher "" -A "MiKroTiK RouterOS" \
                      -input-charset utf-8 \
                      -b isolinux/isolinux.bin \
                      -c isolinux/boot.cat \
                      -no-emul-boot \
                      -boot-load-size 4 \
                      -boot-info-table \
                      -eltorito-alt-boot \
                      -e efiboot.img \
                      -no-emul-boot \
                      -R -J \
                      ./new_iso
        elif [ "${{ matrix.arch }}" == "arm64" ]; then
          sudo -E python3 patch.py kernel ./efiboot/EFI/BOOT/BOOTAA64.EFI
          sudo umount ./efiboot
          sudo xorriso -as mkisofs -o mikrotik-$LATEST_VERSION$ARCH.iso \
                      -V "MikroTik $LATEST_VERSION ${{ matrix.arch }}" \
                      -sysid "" -preparer "MiKroTiK" \
                      -publisher "" -A "MiKroTiK RouterOS" \
                      -input-charset utf-8 \
                      -b efiboot.img \
                      -no-emul-boot \
                      -R -J \
                      ./new_iso
        fi
        sudo rm -rf ./efiboot
        sudo mkdir ./all_packages
        sudo cp ./new_iso/*.npk ./all_packages/
        sudo rm -rf ./new_iso
        cd ./all_packages
        sudo zip ../all_packages-${{ matrix.arch }}-$LATEST_VERSION.zip *.npk
        cd ..

    - name: Cache chr-${{ env.LATEST_VERSION }}${{ env.ARCH }}.img.zip
      if: steps.get_latest.outputs.has_new_version == 'true' && (matrix.arch == 'x86' || matrix.arch == 'arm64')
      id: cache-chr-img
      uses: actions/cache@v4
      with:
        path: |
          chr.img
        key: chr-${{ env.LATEST_VERSION }}${{ env.ARCH }}.img

    - name: Get chr-${{ env.LATEST_VERSION }}${{ env.ARCH }}.img
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-chr-img.outputs.cache-hit != 'true' && (matrix.arch == 'x86' || matrix.arch == 'arm64')
      run: |
          sudo wget -nv -O chr.img.zip https://download.mikrotik.com/routeros/$LATEST_VERSION/chr-$LATEST_VERSION$ARCH.img.zip
          sudo unzip chr.img.zip
          sudo rm chr.img.zip
          sudo mv chr-$LATEST_VERSION$ARCH.img chr.img

    - name: Create chr-${{ env.LATEST_VERSION }}${{ env.ARCH }}.img
      if: steps.get_latest.outputs.has_new_version == 'true' && (matrix.arch == 'x86' || matrix.arch == 'arm64')
      run: |
        sudo modprobe nbd
        sudo apt-get install -y qemu-utils > /dev/null
        truncate --size 128M chr-$LATEST_VERSION$ARCH.img
        sgdisk --clear --set-alignment=2 \
            --new=1::+32M --typecode=1:8300 --change-name=1:"RouterOS Boot" --attributes=1:set:2 \
            --new=2::-0 --typecode=2:8300 --change-name=2:"RouterOS" \
            --gpttombr=1:2 \
            chr-$LATEST_VERSION$ARCH.img
        dd if=chr-$LATEST_VERSION$ARCH.img of=pt.bin bs=1 count=66 skip=446
        echo -e "\x80" | dd of=pt.bin  bs=1 count=1  conv=notrunc
        sgdisk --mbrtogpt --clear --set-alignment=2 \
            --new=1::+32M --typecode=1:8300 --change-name=1:"RouterOS Boot" --attributes=1:set:2 \
            --new=2::-0 --typecode=2:8300 --change-name=2:"RouterOS" \
            chr-$LATEST_VERSION$ARCH.img
        dd if=chr.img of=chr-$LATEST_VERSION$ARCH.img bs=1 count=446 conv=notrunc
        dd if=pt.bin of=chr-$LATEST_VERSION$ARCH.img  bs=1 count=66 seek=446 conv=notrunc
        sudo qemu-nbd -c /dev/nbd0 -f raw chr-$LATEST_VERSION$ARCH.img
        sudo mkfs.vfat -n "Boot" /dev/nbd0p1
        sudo mkfs.ext4 -F -L "RouterOS"  -m 0 /dev/nbd0p2
        sudo mkdir -p ./img/{boot,routeros}
        sudo mount /dev/nbd0p1 ./img/boot/
        if [ "${{ matrix.arch }}" == "x86" ]; then
          sudo cp chr.img chr-$LATEST_VERSION$ARCH-legacy-bios.img 
          sudo qemu-nbd -c /dev/nbd1 -f raw chr-$LATEST_VERSION$ARCH-legacy-bios.img
          sudo -E python3 patch.py block /dev/nbd1p1 EFI/BOOT/BOOTX64.EFI
          sudo mkdir -p ./chr/{boot,routeros}
          sudo mount /dev/nbd1p1 ./chr/boot/
          sudo mkdir -p  ./img/boot/EFI/BOOT
          sudo cp ./chr/boot/EFI/BOOT/BOOTX64.EFI ./img/boot/EFI/BOOT/BOOTX64.EFI
          sudo umount /dev/nbd1p1
          sudo shred -v -n 1 -z /dev/nbd1p2 
          sudo mkfs.ext4 -F -L "RouterOS"  -m 0 /dev/nbd1p2
          sudo mount /dev/nbd1p2 ./chr/routeros/
          sudo mkdir -p ./chr/routeros/{var/pdb/{system,option},boot,rw}
          sudo cp ./all_packages/option-$LATEST_VERSION$ARCH.npk ./chr/routeros/var/pdb/option/image
          sudo cp ./all_packages/routeros-$LATEST_VERSION$ARCH.npk ./chr/routeros/var/pdb/system/image
          sudo umount /dev/nbd1p2
          sudo qemu-nbd -d /dev/nbd1
          sudo rm -rf ./chr

          sudo qemu-img convert -f raw -O qcow2 chr-$LATEST_VERSION$ARCH-legacy-bios.img chr-$LATEST_VERSION$ARCH-legacy-bios.qcow2
          sudo qemu-img convert -f raw -O vmdk chr-$LATEST_VERSION$ARCH-legacy-bios.img chr-$LATEST_VERSION$ARCH-legacy-bios.vmdk
          sudo qemu-img convert -f raw -O vpc chr-$LATEST_VERSION$ARCH-legacy-bios.img chr-$LATEST_VERSION$ARCH-legacy-bios.vhd
          sudo qemu-img convert -f raw -O vhdx chr-$LATEST_VERSION$ARCH-legacy-bios.img chr-$LATEST_VERSION$ARCH-legacy-bios.vhdx
          sudo qemu-img convert -f raw -O vdi chr-$LATEST_VERSION$ARCH-legacy-bios.img chr-$LATEST_VERSION$ARCH-legacy-bios.vdi

          sudo zip chr-$LATEST_VERSION$ARCH-legacy-bios.qcow2.zip chr-$LATEST_VERSION$ARCH-legacy-bios.qcow2
          sudo zip chr-$LATEST_VERSION$ARCH-legacy-bios.vmdk.zip chr-$LATEST_VERSION$ARCH-legacy-bios.vmdk
          sudo zip chr-$LATEST_VERSION$ARCH-legacy-bios.vhd.zip chr-$LATEST_VERSION$ARCH-legacy-bios.vhd
          sudo zip chr-$LATEST_VERSION$ARCH-legacy-bios.vhdx.zip chr-$LATEST_VERSION$ARCH-legacy-bios.vhdx
          sudo zip chr-$LATEST_VERSION$ARCH-legacy-bios.vdi.zip chr-$LATEST_VERSION$ARCH-legacy-bios.vdi
          sudo zip chr-$LATEST_VERSION$ARCH-legacy-bios.img.zip chr-$LATEST_VERSION$ARCH-legacy-bios.img

          sudo rm chr-$LATEST_VERSION$ARCH-legacy-bios.qcow2
          sudo rm chr-$LATEST_VERSION$ARCH-legacy-bios.vmdk
          sudo rm chr-$LATEST_VERSION$ARCH-legacy-bios.vhd
          sudo rm chr-$LATEST_VERSION$ARCH-legacy-bios.vhdx
          sudo rm chr-$LATEST_VERSION$ARCH-legacy-bios.vdi
          sudo rm chr-$LATEST_VERSION$ARCH-legacy-bios.img

        elif [ "${{ matrix.arch }}" == "arm64" ]; then
          sudo qemu-nbd -c /dev/nbd1 -f raw chr.img
          sudo mkdir -p ./chr/boot
          sudo mount /dev/nbd1p1 ./chr/boot/
          sudo -E python3 patch.py kernel ./chr/boot/EFI/BOOT/BOOTAA64.EFI -O ./BOOTAA64.EFI 
          sudo mkdir -p  ./img/boot/EFI/BOOT
          sudo cp ./BOOTAA64.EFI ./img/boot/EFI/BOOT/BOOTAA64.EFI
          sudo umount /dev/nbd1p1
          sudo rm -rf ./chr
          sudo qemu-nbd -d /dev/nbd1
        fi
        sudo umount /dev/nbd0p1
        sudo mount  /dev/nbd0p2 ./img/routeros/
        sudo mkdir -p ./img/routeros/{var/pdb/{system,option},boot,rw}
        sudo cp ./all_packages/option-$LATEST_VERSION$ARCH.npk ./img/routeros/var/pdb/option/image
        sudo cp ./all_packages/routeros-$LATEST_VERSION$ARCH.npk ./img/routeros/var/pdb/system/image
        sudo umount /dev/nbd0p2
        sudo rm -rf ./img
        sudo qemu-nbd -d /dev/nbd0

        sudo qemu-img convert -f raw -O qcow2 chr-$LATEST_VERSION$ARCH.img chr-$LATEST_VERSION$ARCH.qcow2
        sudo qemu-img convert -f raw -O vmdk chr-$LATEST_VERSION$ARCH.img chr-$LATEST_VERSION$ARCH.vmdk
        sudo qemu-img convert -f raw -O vpc chr-$LATEST_VERSION$ARCH.img chr-$LATEST_VERSION$ARCH.vhd
        sudo qemu-img convert -f raw -O vhdx chr-$LATEST_VERSION$ARCH.img chr-$LATEST_VERSION$ARCH.vhdx
        sudo qemu-img convert -f raw -O vdi chr-$LATEST_VERSION$ARCH.img chr-$LATEST_VERSION$ARCH.vdi

        sudo zip chr-$LATEST_VERSION$ARCH.qcow2.zip chr-$LATEST_VERSION$ARCH.qcow2
        sudo zip chr-$LATEST_VERSION$ARCH.vmdk.zip chr-$LATEST_VERSION$ARCH.vmdk
        sudo zip chr-$LATEST_VERSION$ARCH.vhd.zip chr-$LATEST_VERSION$ARCH.vhd
        sudo zip chr-$LATEST_VERSION$ARCH.vhdx.zip chr-$LATEST_VERSION$ARCH.vhdx
        sudo zip chr-$LATEST_VERSION$ARCH.vdi.zip chr-$LATEST_VERSION$ARCH.vdi
        sudo zip chr-$LATEST_VERSION$ARCH.img.zip chr-$LATEST_VERSION$ARCH.img

        sudo rm chr-$LATEST_VERSION$ARCH.qcow2
        sudo rm chr-$LATEST_VERSION$ARCH.vmdk
        sudo rm chr-$LATEST_VERSION$ARCH.vhd
        sudo rm chr-$LATEST_VERSION$ARCH.vhdx
        sudo rm chr-$LATEST_VERSION$ARCH.vdi
        sudo rm chr-$LATEST_VERSION$ARCH.img
    
    - name: Cache all_packages${{ env.ARCH }}-${{ env.LATEST_VERSION }}.zip
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch != 'x86' && matrix.arch != 'arm64'
      id: cache-all-packages
      uses: actions/cache@v4
      with:
        path: all_packages.zip
        key: all_packages${{ env.ARCH }}-${{ env.LATEST_VERSION }}.zip

    - name: Get all_packages{{ env.ARCH }}-${{ env.LATEST_VERSION }}.zip
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-all-packages.outputs.cache-hit != 'true' && matrix.arch != 'x86' && matrix.arch != 'arm64'
      run: |  
        sudo wget -nv -O all_packages.zip https://download.mikrotik.com/routeros/$LATEST_VERSION/all_packages-${{ matrix.arch }}-$LATEST_VERSION.zip

    - name: Cache Main package routeros-${{ env.LATEST_VERSION }}${{ env.ARCH }}.npk
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch != 'x86' && matrix.arch != 'arm64'
      id: cache-main-package
      uses: actions/cache@v4
      with:
        path: routeros.npk
        key: routeros-${{ env.LATEST_VERSION }}${{ env.ARCH }}.npk

    - name: Get Main package routeros-${{ env.LATEST_VERSION }}${{ env.ARCH }}.npk
      if: steps.get_latest.outputs.has_new_version == 'true' && steps.cache-main-package.outputs.cache-hit != 'true' && matrix.arch != 'x86' && matrix.arch != 'arm64'
      run: |
        sudo wget -nv -O routeros.npk https://download.mikrotik.com/routeros/$LATEST_VERSION/routeros-$LATEST_VERSION$ARCH.npk

    - name: Patch all_packages{{ env.ARCH }}-${{ env.LATEST_VERSION }}.zip
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch != 'x86' && matrix.arch != 'arm64'
      run: |
        sudo unzip all_packages.zip -d ./all_packages
        sudo cp routeros.npk routeros-$LATEST_VERSION$ARCH.npk
        sudo -E python3 patch.py npk routeros-$LATEST_VERSION$ARCH.npk
        NPK_FILES=$(find ./all_packages/*.npk)
        for file in $NPK_FILES; do
            sudo -E python3 npk.py sign $file $file
        done
        sudo cp -f routeros-$LATEST_VERSION$ARCH.npk ./all_packages/routeros-$LATEST_VERSION$ARCH.npk
        sudo -E python3 npk.py create ./all_packages/gps-$LATEST_VERSION$ARCH.npk ./all_packages/option-$LATEST_VERSION$ARCH.npk option ./option.sfs -desc="busybox"
        sudo -E python3 npk.py create ./all_packages/gps-$LATEST_VERSION$ARCH.npk ./all_packages/python3-$LATEST_VERSION$ARCH.npk python3 ./python3.sfs -desc="python 3.11.13"
        cd ./all_packages
        sudo zip ../all_packages-${{ matrix.arch }}-$LATEST_VERSION.zip *.npk
        cd ..

    - name: Cache refind
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch == 'x86' && env.RELEASE == 'true'
      id: cache-refind
      uses: actions/cache@v4
      with:
        path: refind-bin-0.14.2.zip
        key: refind

    - name: Get refind
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch == 'x86' && steps.cache-refind.outputs.cache-hit != 'true' && env.RELEASE == 'true'
      run: sudo wget -nv -O refind-bin-0.14.2.zip https://nchc.dl.sourceforge.net/project/refind/0.14.2/refind-bin-0.14.2.zip

    - name: Create install-image-${{ env.LATEST_VERSION }}${{ env.ARCH }}.img
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch == 'x86' && env.RELEASE == 'true'
      run: |
        sudo modprobe nbd
        sudo apt-get install -y qemu-utils extlinux > /dev/null
        truncate --size 128M install-image-$LATEST_VERSION.img
        sudo qemu-nbd -c /dev/nbd0 -f raw install-image-$LATEST_VERSION.img
        sudo mkfs.vfat -n "Install" /dev/nbd0
        sudo mkdir ./install
        sudo mount /dev/nbd0 ./install
        sudo mkdir -p ./install/EFI/BOOT
        sudo unzip refind-bin-0.14.2.zip refind-bin-0.14.2/refind/refind_x64.efi
        sudo cp refind-bin-0.14.2/refind/refind_x64.efi ./install/EFI/BOOT/BOOTX64.EFI
        sudo rm -rf refind-bin-0.14.2
        echo -e 'timeout 0\ntextonly\ntextmode 0\nshowtools shutdown, reboot, exit\nmenuentry "Install RouterOS" {\n\tloader /linux\n\toptions "load_ramdisk=1 root=/dev/ram0 -install -hdd"\n}\ndefault_selection /EFI/BOOT/BOOTX64.EFI' \
          > refind.conf
        sudo cp refind.conf ./install/EFI/BOOT/
        sudo rm refind.conf
        sudo extlinux --install -H 64 -S 32 ./install/
        echo -e 'default system\nLABEL system\n\tKERNEL linux\n\tAPPEND load_ramdisk=1 -install -hdd' \
          > syslinux.cfg
        sudo cp syslinux.cfg ./install/
        sudo rm syslinux.cfg
        sudo cp ./BOOTX64.EFI ./install/linux
        NPK_FILES=($(find ./all_packages/*.npk))
        for ((i=1; i<=${#NPK_FILES[@]}; i++))
        do
          echo "${NPK_FILES[$i-1]}=>$i.npk" 
          sudo cp ${NPK_FILES[$i-1]} ./install/$i.npk
        done
        sudo touch ./install/CHOOSE
        sudo touch ./install/autorun.scr 
        sudo umount /dev/nbd0
        sudo qemu-nbd -d /dev/nbd0
        sudo rm -rf ./install
        sudo zip install-image-$LATEST_VERSION.zip ./install-image-$LATEST_VERSION.img
        sudo rm ./install-image-$LATEST_VERSION.img


    - name: Cache NetInstall ${{ env.LATEST_VERSION }}${{ env.ARCH }}
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch == 'x86' && env.RELEASE == 'true'
      id: cache-netinstall
      uses: actions/cache@v4
      with:
        path: |
          netinstall.zip
          netinstall.tar.gz
        key: netinstall-${{ env.LATEST_VERSION }}

    - name: Get netinstall ${{ env.LATEST_VERSION }}
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch == 'x86' && steps.cache-netinstall.outputs.cache-hit != 'true' && env.RELEASE == 'true'
      run: |
        sudo wget -nv -O netinstall.zip https://download.mikrotik.com/routeros/$LATEST_VERSION/netinstall-$LATEST_VERSION.zip
        sudo wget -nv -O netinstall.tar.gz https://download.mikrotik.com/routeros/$LATEST_VERSION/netinstall-$LATEST_VERSION.tar.gz

    - name: Patch netinstall ${{ env.LATEST_VERSION }}${{ env.ARCH }}
      if: steps.get_latest.outputs.has_new_version == 'true' && matrix.arch == 'x86' && env.RELEASE == 'true'
      run: |
        sudo unzip netinstall.zip
        sudo -E python3 patch.py netinstall netinstall.exe
        sudo zip netinstall-$LATEST_VERSION.zip ./netinstall.exe
        sudo tar -xvf netinstall.tar.gz
        sudo -E python3 patch.py netinstall netinstall-cli
        sudo tar -czvf netinstall-$LATEST_VERSION.tar.gz ./netinstall-cli

    - name: Upload Files
      if: steps.get_latest.outputs.has_new_version == 'true' && env.RELEASE == 'true'
      run: |
        sudo mkdir -p ./publish/$LATEST_VERSION
        sudo cp CHANGELOG ./publish/$LATEST_VERSION/
        sudo cp ./all_packages/*.npk ./publish/$LATEST_VERSION/
        sudo chown -R root:root ./publish/
        
        LOCAL_PATH=./publish/$LATEST_VERSION
        REMOTE_PATH=${{ secrets.SSH_DIRECTORY }}
        SERVER=${{ secrets.SSH_SERVER }}
        USER=${{ secrets.SSH_USERNAME }}
        PASS=${{ secrets.SSH_PASSWORD }}
        PORT=${{ secrets.SSH_PORT }}
        CHANNEL=${{ matrix.channel }}

        sudo apt-get install -y lftp ssh sshpass > /dev/null 2>&1

        sudo -E lftp -u "$USER","$PASS" sftp://$SERVER:$PORT <<EOF
        set sftp:auto-confirm yes
        mirror --reverse --verbose --only-newer ./publish "$REMOTE_PATH"
        bye
        EOF

        sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no -p $PORT $USER@$SERVER \
          "echo $LATEST_VERSION $BUILD_TIME | tee /rw/disk/$REMOTE_PATH/NEWESTa7.$CHANNEL; bash /rw/disk/$REMOTE_PATH/packages.sh /rw/disk/$REMOTE_PATH/$LATEST_VERSION"

        sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no -p $PORT $USER@$SERVER \
          "chown -R 32768:32768 /rw/disk/$REMOTE_PATH/"



    - name: Clear Cloudflare cache
      if: steps.get_latest.outputs.has_new_version == 'true' && env.RELEASE == 'true'
      run: |
        curl --request POST --url "https://api.cloudflare.com/client/v4/zones/fe6831ed6dc9e6235e69ef2a31f2e7fe/purge_cache" \
            --header "Authorization: Bearer 9GDQkzU51QXaqzp1qMjyFKpyeJyOdnNoG9GZQaGP" \
            --header "Content-Type:application/json" \
            --data '{"purge_everything": true}'

    - name: Delete Release tag ${{ env.LATEST_VERSION }} ${{ env.ARCH }}
      if: steps.get_latest.outputs.has_new_version == 'true' && env.RELEASE == 'true'
      run: |
        HEADER="Authorization: token ${{ secrets.GITHUB_TOKEN }}"
        RELEASE_INFO=$(curl -s -H "$HEADER" https://api.github.com/repos/${{ github.repository }}/releases/tags/$LATEST_VERSION$ARCH)
        RELEASE_ID=$(echo $RELEASE_INFO | jq -r '.id')
        echo "Release ID: $RELEASE_ID"
        if [ "$RELEASE_ID" != "null" ]; then
            curl -X DELETE -H "$HEADER" https://api.github.com/repos/${{ github.repository }}/git/refs/tags/$LATEST_VERSION$ARCH
            echo "Tag $LATEST_VERSION$ARCH deleted successfully."
            curl -X DELETE -H "$HEADER" https://api.github.com/repos/${{ github.repository }}/releases/$RELEASE_ID
            echo "Release with tag $LATEST_VERSION$ARCH deleted successfully."
        else
            echo "Release not found for tag: $LATEST_VERSION)"
        fi

    - name: Create Release tag ${{ env.LATEST_VERSION }} ${{ env.ARCH }}
      if: steps.get_latest.outputs.has_new_version == 'true' && env.RELEASE == 'true'
      uses: softprops/action-gh-release@v2
      with:
        name: "RouterOS ${{ env.LATEST_VERSION }} ${{ env.ARCH }}"
        body_path: "CHANGELOG"
        tag_name: ${{ env.LATEST_VERSION }}${{ env.ARCH }}
        make_latest:  ${{ matrix.channel == 'stable'}} && ${{ matrix.arch == 'x86'}}
        prerelease:  ${{ matrix.channel == 'testing' }}
        files: |
          mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}.iso
          chr-${{ env.LATEST_VERSION }}*.zip
          netinstall-${{ env.LATEST_VERSION }}.*
          install-image-${{ env.LATEST_VERSION }}.zip
          routeros-${{ env.LATEST_VERSION }}${{ env.ARCH }}.npk
          all_packages-*-${{ env.LATEST_VERSION }}.zip
          
    - name: Upload Files as Artifact (No Release)
      if: steps.get_latest.outputs.has_new_version == 'true' && env.RELEASE == 'false'
      uses: actions/upload-artifact@v4
      with:
        name: mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}
        path: |
          all_packages-*-${{ env.LATEST_VERSION }}.zip
          mikrotik-${{ env.LATEST_VERSION }}${{ env.ARCH }}.iso
          chr-${{ env.LATEST_VERSION }}*.zip
