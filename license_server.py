#!/usr/bin/env python3
"""
MikroTik License Server Simulation
Simulates MikroTik's license validation and management servers.
"""

import json
import datetime
import hashlib
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from license_manager import LicenseManager
from mikrotik_keygen import MikroTikLicenseGenerator

class LicenseServerHandler(BaseHTTPRequestHandler):
    """HTTP handler for license server requests"""
    
    def __init__(self, *args, license_manager=None, **kwargs):
        self.license_manager = license_manager or LicenseManager()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        params = parse_qs(parsed_url.query)
        
        if path == '/license/validate':
            self.handle_license_validation(params)
        elif path == '/license/info':
            self.handle_license_info(params)
        elif path == '/upgrade/check':
            self.handle_upgrade_check(params)
        elif path == '/cloud/backup':
            self.handle_cloud_backup(params)
        elif path == '/status':
            self.handle_status()
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        if path == '/license/activate':
            self.handle_license_activation(post_data)
        elif path == '/license/renew':
            self.handle_license_renewal(post_data)
        elif path == '/cloud/sync':
            self.handle_cloud_sync(post_data)
        else:
            self.send_error(404, "Not Found")
    
    def handle_license_validation(self, params):
        """Handle license validation requests"""
        software_id = params.get('software_id', [''])[0]
        license_key = params.get('license_key', [''])[0]
        
        if not software_id or not license_key:
            self.send_json_response({'error': 'Missing parameters'}, 400)
            return
        
        # Validate license
        is_valid = self.license_manager.validate_license(license_key)
        
        response = {
            'software_id': software_id,
            'valid': is_valid,
            'timestamp': datetime.datetime.now().isoformat(),
            'server': 'Custom MikroTik License Server'
        }
        
        if is_valid:
            response.update({
                'status': 'active',
                'expires': (datetime.datetime.now() + datetime.timedelta(days=365)).isoformat(),
                'level': 4,  # Default level
                'features': ['routing', 'wireless', 'hotspot', 'mpls', 'advanced-tools']
            })
        else:
            response.update({
                'status': 'invalid',
                'reason': 'License signature verification failed'
            })
        
        self.send_json_response(response)
    
    def handle_license_info(self, params):
        """Handle license information requests"""
        software_id = params.get('software_id', [''])[0]
        
        if not software_id:
            self.send_json_response({'error': 'Missing software_id'}, 400)
            return
        
        # Get license info from database
        licenses = self.license_manager.db.get_licenses()
        license_info = None
        
        for license_data in licenses:
            if license_data[1] == software_id:  # software_id column
                license_info = {
                    'software_id': license_data[1],
                    'version': license_data[2],
                    'level': license_data[3],
                    'created': license_data[9],
                    'expires': license_data[10],
                    'active': bool(license_data[11]),
                    'customer': license_data[12]
                }
                break
        
        if license_info:
            self.send_json_response(license_info)
        else:
            self.send_json_response({'error': 'License not found'}, 404)
    
    def handle_license_activation(self, post_data):
        """Handle license activation requests"""
        try:
            data = json.loads(post_data)
            software_id = data.get('software_id')
            hardware_id = data.get('hardware_id')
            version = data.get('version', 7)
            
            if not software_id or not hardware_id:
                self.send_json_response({'error': 'Missing required fields'}, 400)
                return
            
            # Generate new license for activation
            license_id, license_key = self.license_manager.create_license(
                software_id=software_id,
                version=version,
                level=4
            )
            
            response = {
                'success': True,
                'license_id': license_id,
                'license_key': license_key,
                'activated_at': datetime.datetime.now().isoformat(),
                'expires_at': (datetime.datetime.now() + datetime.timedelta(days=365)).isoformat()
            }
            
            self.send_json_response(response)
            
        except json.JSONDecodeError:
            self.send_json_response({'error': 'Invalid JSON'}, 400)
    
    def handle_license_renewal(self, post_data):
        """Handle license renewal requests"""
        try:
            data = json.loads(post_data)
            software_id = data.get('software_id')
            
            if not software_id:
                self.send_json_response({'error': 'Missing software_id'}, 400)
                return
            
            # Simulate renewal
            response = {
                'success': True,
                'software_id': software_id,
                'renewed_at': datetime.datetime.now().isoformat(),
                'expires_at': (datetime.datetime.now() + datetime.timedelta(days=365)).isoformat(),
                'renewal_fee': 0.00  # Free renewal for custom licenses
            }
            
            self.send_json_response(response)
            
        except json.JSONDecodeError:
            self.send_json_response({'error': 'Invalid JSON'}, 400)
    
    def handle_upgrade_check(self, params):
        """Handle upgrade check requests"""
        current_version = params.get('version', [''])[0]
        channel = params.get('channel', ['stable'])[0]
        
        # Simulate upgrade information
        response = {
            'current_version': current_version,
            'latest_version': '7.19.4',
            'channel': channel,
            'upgrade_available': current_version != '7.19.4',
            'download_url': f'https://your-upgrade-server.com/routeros/7.19.4/mikrotik-7.19.4.npk',
            'changelog': 'Custom RouterOS build with enhanced features',
            'size_mb': 45.2,
            'release_date': '2024-01-15'
        }
        
        self.send_json_response(response)
    
    def handle_cloud_backup(self, params):
        """Handle cloud backup requests"""
        device_id = params.get('device_id', [''])[0]
        
        response = {
            'device_id': device_id,
            'backup_available': True,
            'last_backup': datetime.datetime.now().isoformat(),
            'backup_size_kb': 156.7,
            'cloud_storage_used_mb': 2.3,
            'cloud_storage_limit_mb': 100.0
        }
        
        self.send_json_response(response)
    
    def handle_cloud_sync(self, post_data):
        """Handle cloud sync requests"""
        try:
            data = json.loads(post_data)
            device_id = data.get('device_id')
            config_data = data.get('config_data')
            
            response = {
                'success': True,
                'device_id': device_id,
                'synced_at': datetime.datetime.now().isoformat(),
                'config_hash': hashlib.md5(config_data.encode()).hexdigest() if config_data else None
            }
            
            self.send_json_response(response)
            
        except json.JSONDecodeError:
            self.send_json_response({'error': 'Invalid JSON'}, 400)
    
    def handle_status(self):
        """Handle server status requests"""
        response = {
            'server': 'Custom MikroTik License Server',
            'version': '1.0.0',
            'status': 'online',
            'timestamp': datetime.datetime.now().isoformat(),
            'services': {
                'license_validation': 'online',
                'license_activation': 'online',
                'upgrade_check': 'online',
                'cloud_backup': 'online'
            },
            'statistics': {
                'total_licenses': len(self.license_manager.db.get_licenses()),
                'active_licenses': len(self.license_manager.db.get_licenses(active_only=True)),
                'uptime_hours': 24.5
            }
        }
        
        self.send_json_response(response)
    
    def send_json_response(self, data, status_code=200):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        json_data = json.dumps(data, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

class LicenseServer:
    """MikroTik License Server"""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.license_manager = LicenseManager()
    
    def start(self):
        """Start the license server"""
        def handler(*args, **kwargs):
            return LicenseServerHandler(*args, license_manager=self.license_manager, **kwargs)
        
        server = HTTPServer((self.host, self.port), handler)
        
        print(f"Custom MikroTik License Server starting...")
        print(f"Server: http://{self.host}:{self.port}")
        print(f"Status: http://{self.host}:{self.port}/status")
        print(f"License validation: http://{self.host}:{self.port}/license/validate")
        print(f"Press Ctrl+C to stop")
        
        try:
            server.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down server...")
            server.shutdown()

def main():
    """Main server function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MikroTik License Server')
    parser.add_argument('--host', default='localhost', help='Server host')
    parser.add_argument('--port', type=int, default=8080, help='Server port')
    
    args = parser.parse_args()
    
    server = LicenseServer(args.host, args.port)
    server.start()

if __name__ == '__main__':
    main()
