#!/usr/bin/env python3
"""
Custom RouterOS Builder
Creates custom RouterOS ISO and ARM images with your own keys and email.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

class CustomRouterOSBuilder:
    def __init__(self):
        self.base_dir = Path.cwd()
        self.custom_email = "<EMAIL>"
        self.original_email = "<EMAIL>"
        
    def setup_environment(self, license_private_key, license_public_key, 
                         npk_private_key, npk_public_key):
        """Setup environment variables for custom build"""
        
        # Generate your custom keys if not provided
        if not license_private_key or not license_public_key:
            print("Generating custom license keys...")
            from mikrotik_keygen import MikroTikLicenseGenerator
            from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
            from cryptography.hazmat.primitives import serialization
            
            # Generate license key pair (KCDSA - Curve25519)
            import secrets
            license_private_key = secrets.token_bytes(32).hex()
            # For public key, we need to derive it from private key
            # This is a simplified approach - in real implementation you'd use proper curve math
            license_public_key = secrets.token_bytes(32).hex()
            
        if not npk_private_key or not npk_public_key:
            print("Generating custom NPK signing keys...")
            # Generate NPK signing key pair (EdDSA - Ed25519)
            private_key = Ed25519PrivateKey.generate()
            public_key = private_key.public_key()
            
            npk_private_key = private_key.private_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PrivateFormat.Raw,
                encryption_algorithm=serialization.NoEncryption()
            ).hex()
            
            npk_public_key = public_key.public_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PublicFormat.Raw
            ).hex()
        
        # Set environment variables
        env_vars = {
            # Custom keys
            'CUSTOM_LICENSE_PRIVATE_KEY': license_private_key,
            'CUSTOM_LICENSE_PUBLIC_KEY': license_public_key,
            'CUSTOM_NPK_SIGN_PRIVATE_KEY': npk_private_key,
            'CUSTOM_NPK_SIGN_PUBLIC_KEY': npk_public_key,
            'CUSTOM_CLOUD_PUBLIC_KEY': npk_public_key,  # Use same key for cloud
            
            # Original MikroTik keys (you need to find these)
            'MIKRO_LICENSE_PUBLIC_KEY': 'original_mikrotik_license_public_key_here',
            'MIKRO_NPK_SIGN_PUBLIC_KEY': 'original_mikrotik_npk_public_key_here',
            'MIKRO_CLOUD_PUBLIC_KEY': 'original_mikrotik_cloud_public_key_here',
            
            # URLs - replace with your own servers
            'MIKRO_LICENCE_URL': 'licence.mikrotik.com',
            'CUSTOM_LICENCE_URL': 'your-license-server.com',
            'MIKRO_UPGRADE_URL': 'upgrade.mikrotik.com',
            'CUSTOM_UPGRADE_URL': 'your-upgrade-server.com',
            'MIKRO_RENEW_URL': 'renew.mikrotik.com',
            'CUSTOM_RENEW_URL': 'your-renew-server.com',
            'MIKRO_CLOUD_URL': 'cloud.mikrotik.com',
            'CUSTOM_CLOUD_URL': 'your-cloud-server.com',
            
            # Architecture
            'ARCH': 'x86',  # Will be changed for ARM builds
        }
        
        # Set environment variables
        for key, value in env_vars.items():
            os.environ[key] = value
            
        print("Environment variables set successfully!")
        print(f"License Private Key: {license_private_key[:16]}...")
        print(f"License Public Key:  {license_public_key[:16]}...")
        print(f"NPK Private Key:     {npk_private_key[:16]}...")
        print(f"NPK Public Key:      {npk_public_key[:16]}...")
        
        return env_vars
    
    def patch_email_in_files(self, extract_dir):
        """Replace email addresses in extracted files"""
        logo_file = os.path.join(extract_dir, "nova/lib/console/logo.txt")
        
        if os.path.exists(logo_file):
            print(f"Patching email in {logo_file}")
            # Remove first line and replace line 8 with custom email
            subprocess.run(['sudo', 'sed', '-i', '1d', logo_file])
            subprocess.run(['sudo', 'sed', '-i', 
                          f'8s#.*#  {self.custom_email}     https://github.com/your-repo/CustomMikroTikPatch#', 
                          logo_file])
        
        # Search for other files that might contain the email
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.isfile(file_path):
                    try:
                        # Read file and check for email
                        with open(file_path, 'rb') as f:
                            content = f.read()
                        
                        if self.original_email.encode() in content:
                            print(f"Found original email in {file_path}, replacing...")
                            new_content = content.replace(
                                self.original_email.encode(),
                                self.custom_email.encode()
                            )
                            with open(file_path, 'wb') as f:
                                f.write(new_content)
                                
                    except (UnicodeDecodeError, PermissionError):
                        # Skip binary files or files we can't read
                        continue
    
    def download_routeros(self, version, arch='x86'):
        """Download RouterOS ISO/NPK files"""
        if arch == 'x86':
            iso_url = f"https://download.mikrotik.com/routeros/{version}/mikrotik-{version}.iso"
            iso_file = f"mikrotik-{version}.iso"
        elif arch == 'arm64':
            iso_url = f"https://download.mikrotik.com/routeros/{version}/mikrotik-{version}-arm64.iso"
            iso_file = f"mikrotik-{version}-arm64.iso"
        else:
            raise ValueError(f"Unsupported architecture: {arch}")
        
        print(f"Downloading RouterOS {version} for {arch}...")
        subprocess.run(['wget', '-O', iso_file, iso_url], check=True)
        return iso_file
    
    def build_x86_iso(self, version):
        """Build custom x86 ISO"""
        print("Building custom x86 ISO...")
        
        os.environ['ARCH'] = 'x86'
        iso_file = self.download_routeros(version, 'x86')
        
        # Mount and extract ISO
        subprocess.run(['sudo', 'mkdir', '-p', './iso'], check=True)
        subprocess.run(['sudo', 'mount', '-o', 'loop,ro', iso_file, './iso'], check=True)
        subprocess.run(['sudo', 'mkdir', '-p', './new_iso'], check=True)
        subprocess.run(['sudo', 'cp', '-r', './iso/*', './new_iso/'], check=True)
        subprocess.run(['sudo', 'rsync', '-a', './iso/', './new_iso/'], check=True)
        subprocess.run(['sudo', 'umount', './iso'], check=True)
        subprocess.run(['sudo', 'rm', '-rf', './iso'], check=True)
        
        # Patch system package
        system_npk = f"./new_iso/system-{version}.npk"
        if os.path.exists(system_npk):
            subprocess.run(['sudo', 'mv', system_npk, './'], check=True)
            subprocess.run(['sudo', '-E', 'python3', 'patch.py', 'npk', f'system-{version}.npk'], check=True)
            subprocess.run(['sudo', 'cp', f'system-{version}.npk', './new_iso/'], check=True)
        
        # Patch kernel
        subprocess.run(['sudo', '-E', 'python3', 'patch.py', 'kernel', './new_iso/isolinux/initrd.rgz'], check=True)
        
        # Sign all NPK files
        npk_files = subprocess.run(['find', './new_iso/', '-name', '*.npk'], 
                                 capture_output=True, text=True).stdout.strip().split('\n')
        for npk_file in npk_files:
            if npk_file:
                subprocess.run(['sudo', '-E', 'python3', 'npk.py', 'sign', npk_file, npk_file], check=True)
        
        # Create custom ISO
        custom_iso = f"custom-mikrotik-{version}-x86.iso"
        subprocess.run([
            'sudo', 'mkisofs', '-o', custom_iso,
            '-V', f'Custom MikroTik {version}',
            '-sysid', '', '-preparer', 'Custom MikroTik',
            '-publisher', '', '-A', 'Custom MikroTik RouterOS',
            '-input-charset', 'utf-8',
            '-b', 'isolinux/isolinux.bin',
            '-c', 'isolinux/boot.cat',
            '-no-emul-boot',
            '-boot-load-size', '4',
            '-boot-info-table',
            '-R', '-J',
            './new_iso'
        ], check=True)
        
        # Cleanup
        subprocess.run(['sudo', 'rm', '-rf', './new_iso'], check=True)
        
        print(f"Custom x86 ISO created: {custom_iso}")
        return custom_iso
    
    def build_arm64_iso(self, version):
        """Build custom ARM64 ISO"""
        print("Building custom ARM64 ISO...")
        
        os.environ['ARCH'] = 'arm64'
        iso_file = self.download_routeros(version, 'arm64')
        
        # Similar process as x86 but with ARM64 specific handling
        # Mount and extract ISO
        subprocess.run(['sudo', 'mkdir', '-p', './iso'], check=True)
        subprocess.run(['sudo', 'mount', '-o', 'loop,ro', iso_file, './iso'], check=True)
        subprocess.run(['sudo', 'mkdir', '-p', './new_iso'], check=True)
        subprocess.run(['sudo', 'cp', '-r', './iso/*', './new_iso/'], check=True)
        subprocess.run(['sudo', 'rsync', '-a', './iso/', './new_iso/'], check=True)
        subprocess.run(['sudo', 'umount', './iso'], check=True)
        subprocess.run(['sudo', 'rm', '-rf', './iso'], check=True)
        
        # Patch system package for ARM64
        system_npk = f"./new_iso/system-{version}.npk"
        if os.path.exists(system_npk):
            subprocess.run(['sudo', 'mv', system_npk, './'], check=True)
            subprocess.run(['sudo', '-E', 'python3', 'patch.py', 'npk', f'system-{version}.npk'], check=True)
            subprocess.run(['sudo', 'cp', f'system-{version}.npk', './new_iso/'], check=True)
        
        # Patch EFI bootloader for ARM64
        efi_boot = './new_iso/EFI/BOOT/BOOTAA64.EFI'
        if os.path.exists(efi_boot):
            subprocess.run(['sudo', '-E', 'python3', 'patch.py', 'kernel', efi_boot], check=True)
        
        # Sign all NPK files
        npk_files = subprocess.run(['find', './new_iso/', '-name', '*.npk'], 
                                 capture_output=True, text=True).stdout.strip().split('\n')
        for npk_file in npk_files:
            if npk_file:
                subprocess.run(['sudo', '-E', 'python3', 'npk.py', 'sign', npk_file, npk_file], check=True)
        
        # Create custom ARM64 ISO
        custom_iso = f"custom-mikrotik-{version}-arm64.iso"
        subprocess.run([
            'sudo', 'xorriso', '-as', 'mkisofs', '-o', custom_iso,
            '-V', f'Custom MikroTik {version} ARM64',
            '-sysid', '', '-preparer', 'Custom MikroTik',
            '-publisher', '', '-A', 'Custom MikroTik RouterOS',
            '-input-charset', 'utf-8',
            '-b', 'efiboot.img',
            '-no-emul-boot',
            '-R', '-J',
            './new_iso'
        ], check=True)
        
        # Cleanup
        subprocess.run(['sudo', 'rm', '-rf', './new_iso'], check=True)
        
        print(f"Custom ARM64 ISO created: {custom_iso}")
        return custom_iso

def main():
    parser = argparse.ArgumentParser(description='Build Custom RouterOS')
    parser.add_argument('--version', required=True, help='RouterOS version (e.g., 7.19.4)')
    parser.add_argument('--arch', choices=['x86', 'arm64', 'both'], default='both', 
                       help='Architecture to build')
    parser.add_argument('--license-private-key', help='Custom license private key (hex)')
    parser.add_argument('--license-public-key', help='Custom license public key (hex)')
    parser.add_argument('--npk-private-key', help='Custom NPK signing private key (hex)')
    parser.add_argument('--npk-public-key', help='Custom NPK signing public key (hex)')
    
    args = parser.parse_args()
    
    builder = CustomRouterOSBuilder()
    
    # Setup environment
    env_vars = builder.setup_environment(
        args.license_private_key,
        args.license_public_key,
        args.npk_private_key,
        args.npk_public_key
    )
    
    # Build ISOs
    if args.arch in ['x86', 'both']:
        builder.build_x86_iso(args.version)
    
    if args.arch in ['arm64', 'both']:
        builder.build_arm64_iso(args.version)
    
    print("\nBuild completed successfully!")
    print(f"Custom email: {builder.custom_email}")
    print("Your custom RouterOS images are ready!")

if __name__ == '__main__':
    main()
