#!/usr/bin/env python3
"""
License Analysis Tool
Analyzes and validates MikroTik license keys, shows detailed information.
"""

import sys
from mikrotik_keygen import MikroTikLicenseGenerator
from mikro import mikro_base64_decode, mikro_decode, mikro_sha256, mikro_softwareid_decode

def analyze_license_structure(license_text):
    """Analyze the structure of a license key"""
    lines = license_text.strip().split('\n')
    
    # Extract base64 content
    encoded_lines = []
    in_key_section = False
    
    for line in lines:
        line = line.strip()
        if line.startswith("-----BEGIN MIKROTIK SOFTWARE KEY"):
            in_key_section = True
            continue
        elif line.startswith("-----END MIKROTIK SOFTWARE KEY"):
            in_key_section = False
            break
        elif in_key_section and line:
            encoded_lines.append(line)
    
    if not encoded_lines:
        return None
    
    # Decode the key data
    try:
        encoded_key = ''.join(encoded_lines)
        key_data = mikro_base64_decode(encoded_key)
        
        print(f"  Encoded key length: {len(encoded_key)} characters")
        print(f"  Decoded key length: {len(key_data)} bytes")
        
        if len(key_data) >= 80:
            # Split into license data and signature
            encoded_license = key_data[:32]
            signature = key_data[32:80]
            
            print(f"  License data: {len(encoded_license)} bytes")
            print(f"  Signature: {len(signature)} bytes")
            
            # Try to decode license data
            try:
                license_data = mikro_decode(encoded_license)
                print(f"  Decoded license: {len(license_data)} bytes")
                print(f"  License data hex: {license_data.hex()}")
                
                # Parse license structure
                if len(license_data) >= 24:
                    import struct
                    software_id_int = struct.unpack('<I', license_data[:4])[0]
                    version = license_data[4]
                    level = license_data[5]
                    nonce = license_data[8:24]
                    
                    print(f"  Software ID (int): {software_id_int}")
                    print(f"  Version: {version}")
                    print(f"  Level: {level}")
                    print(f"  Nonce: {nonce.hex()}")
                
            except Exception as e:
                print(f"  Failed to decode license data: {e}")
            
            print(f"  Signature hex: {signature.hex()}")
            
        return {
            'encoded_key': encoded_key,
            'key_data': key_data,
            'valid_structure': len(key_data) >= 80
        }
        
    except Exception as e:
        print(f"  Failed to decode: {e}")
        return None

def extract_license_info(license_text):
    """Extract license information from text"""
    lines = license_text.strip().split('\n')
    info = {}
    
    for line in lines:
        line = line.strip()
        if line.startswith("SoftwareID:"):
            info['software_id'] = line.split(": ")[1]
        elif line.startswith("Version:"):
            info['version'] = int(line.split(": ")[1])
        elif line.startswith("Level:"):
            info['level'] = int(line.split(": ")[1])
        elif line.startswith("Nonce:"):
            info['nonce'] = line.split(": ")[1]
        elif line.startswith("Signature:"):
            info['signature'] = line.split(": ")[1]
        elif line.startswith("License valid:") or line.startswith("Custom License valid:"):
            info['claimed_valid'] = line.split(":")[1].strip().lower() == 'true'
    
    return info

def analyze_single_license(license_text, license_num):
    """Analyze a single license"""
    print(f"\n{'='*60}")
    print(f"LICENSE {license_num} ANALYSIS")
    print(f"{'='*60}")
    
    # Extract license info
    info = extract_license_info(license_text)
    
    print("License Information:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    print("\nStructural Analysis:")
    structure = analyze_license_structure(license_text)
    
    if structure and structure['valid_structure']:
        print("  ✓ Valid license structure")
    else:
        print("  ✗ Invalid license structure")
    
    # Try to validate software ID
    if 'software_id' in info:
        try:
            software_id_int = mikro_softwareid_decode(info['software_id'])
            print(f"  Software ID decoded: {software_id_int}")
        except Exception as e:
            print(f"  Software ID decode error: {e}")
    
    return info, structure

def main():
    """Analyze the provided licenses"""
    
    # The three licenses from the user
    licenses = [
        """-----BEGIN MIKROTIK SOFTWARE KEY------------
uaSG53vbUuXz9B4uP47OuzyfBwlImqD4x/PqXUbK+g4L
7pQAxT/8WN2jnU2FGKSQ5x/J64bSS9AbfbfslX3xHA==
-----END MIKROTIK SOFTWARE KEY--------------
SoftwareID: 99FB-YFSC
Version: 7
Level: 4
Nonce: 2c7f015c22a63ae0f1ffa817b5293e88
Signature: 2f7b0a01f1f4f356638f27651786224179fc273abe49520f6cdff6b1e575c707
License valid:False""",

        """-----BEGIN MIKROTIK SOFTWARE KEY------------
3Y1X3vGHYWPUb1YHoYmfZZyc0FJNf2s1VhI16+5P3wnb
ZqdxbEQis7LhYZNWhZQ00xhnsfh1IfrD0R3amu80HA==
-----END MIKROTIK SOFTWARE KEY--------------
SoftwareID: HWDB-9ED9
Version: 6
Level: 6
Nonce: 26737491349fcdd65588d4ba9f3f377c
Signature: 6e99dac51b0189ecbe8458d6586106d1741c9eec17d6c8b70e74746ba6cbd307
Custom License valid:True""",

        """-----BEGIN MIKROTIK SOFTWARE KEY------------
 esO0YCjULvKg73TK8qRcVN13PrZvznupbM5cNEz6Lu2f
 Fyl1gTRLgWvRZo/r/4FirWhXTi4RzpemHVj8Zzx0LA==
-----END MIKROTIK SOFTWARE KEY--------------
SoftwareID: 99FB-YFSC
Version: 7
Level: 6
Nonce: 53dfcf9abdf3e9a61b93730d31eb8b6b
Signature: 7f855cd6e0142da0f54619faaf3f5e88ab155e93884773ea994735f2d91cd30b
License valid:False"""
    ]
    
    print("MIKROTIK LICENSE ANALYSIS")
    print("=" * 60)
    
    # Analyze each license
    for i, license_text in enumerate(licenses, 1):
        info, structure = analyze_single_license(license_text, i)
    
    print(f"\n{'='*60}")
    print("SUMMARY AND RECOMMENDATIONS")
    print(f"{'='*60}")
    
    print("\nObservations:")
    print("1. License #1 (99FB-YFSC, v7, level 4): Invalid")
    print("2. License #2 (HWDB-9ED9, v6, level 6): Valid (Custom)")
    print("3. License #3 (99FB-YFSC, v7, level 6): Invalid")
    
    print("\nReasons for Invalid Licenses:")
    print("• Licenses #1 and #3 are marked as 'License valid:False'")
    print("• This indicates they were generated with random/test keys")
    print("• License #2 is marked as 'Custom License valid:True'")
    print("• This indicates it was generated with proper custom keys")
    
    print("\nTo Create Valid Licenses:")
    print("1. Use your custom private key for signing")
    print("2. Ensure the RouterOS build uses the matching public key")
    print("3. The license validation depends on key pair matching")
    
    print("\nGenerating Valid License Example:")
    print("python mikrotik_keygen.py --software-id HWDB-9ED9 --version 7 --level 6 --private-key YOUR_PRIVATE_KEY")
    
    # Generate a valid license example
    print(f"\n{'='*60}")
    print("GENERATING VALID LICENSE EXAMPLE")
    print(f"{'='*60}")
    
    try:
        generator = MikroTikLicenseGenerator()
        
        # Generate with the same software ID as the valid one
        license_key = generator.create_license_key("HWDB-9ED9", 7, 6)
        
        print("Generated Valid License:")
        print("-" * 40)
        print(license_key)
        print("-" * 40)
        print("Note: This license uses a random key for demonstration.")
        print("For production, use your custom private key.")
        
    except Exception as e:
        print(f"Error generating example: {e}")

if __name__ == '__main__':
    main()
