#!/usr/bin/env python3
"""
Email Replacement System for RouterOS
Comprehensive system to replace email addresses throughout RouterOS files.
"""

import os
import re
import sys
import mmap
import struct
import binascii
from pathlib import Path

class EmailReplacer:
    """System to replace email addresses in RouterOS files"""
    
    def __init__(self, old_email="<EMAIL>", new_email="<EMAIL>"):
        self.old_email = old_email
        self.new_email = new_email
        self.old_email_bytes = old_email.encode('utf-8')
        self.new_email_bytes = new_email.encode('utf-8')
        
        # Ensure new email is not longer than old email
        if len(self.new_email_bytes) > len(self.old_email_bytes):
            # Truncate new email if too long
            self.new_email_bytes = self.new_email_bytes[:len(self.old_email_bytes)]
            self.new_email = self.new_email_bytes.decode('utf-8')
            print(f"Warning: New email truncated to: {self.new_email}")
        
        self.replacements_made = 0
        self.files_processed = 0
    
    def replace_in_file(self, file_path):
        """Replace email in a single file"""
        try:
            # Read file content
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Check if old email exists
            if self.old_email_bytes not in content:
                return False
            
            # Replace email
            new_content = content.replace(self.old_email_bytes, self.new_email_bytes)
            
            # Pad with null bytes if new email is shorter
            if len(self.new_email_bytes) < len(self.old_email_bytes):
                padding_length = len(self.old_email_bytes) - len(self.new_email_bytes)
                # Replace with new email + null padding
                new_content = content.replace(
                    self.old_email_bytes,
                    self.new_email_bytes + b'\x00' * padding_length
                )
            
            # Write back to file
            with open(file_path, 'wb') as f:
                f.write(new_content)
            
            self.replacements_made += 1
            return True
            
        except (IOError, OSError, PermissionError) as e:
            print(f"Error processing {file_path}: {e}")
            return False
    
    def replace_in_binary(self, file_path):
        """Replace email in binary files using memory mapping"""
        try:
            with open(file_path, 'r+b') as f:
                with mmap.mmap(f.fileno(), 0) as mm:
                    # Find all occurrences
                    pos = 0
                    replacements = 0
                    
                    while True:
                        pos = mm.find(self.old_email_bytes, pos)
                        if pos == -1:
                            break
                        
                        # Replace in-place
                        mm[pos:pos+len(self.old_email_bytes)] = (
                            self.new_email_bytes + 
                            b'\x00' * (len(self.old_email_bytes) - len(self.new_email_bytes))
                        )
                        
                        pos += len(self.old_email_bytes)
                        replacements += 1
                    
                    if replacements > 0:
                        self.replacements_made += replacements
                        return True
            
            return False
            
        except (IOError, OSError, PermissionError) as e:
            print(f"Error processing binary {file_path}: {e}")
            return False
    
    def replace_in_logo_file(self, logo_path):
        """Special handling for logo.txt file"""
        try:
            if not os.path.exists(logo_path):
                return False
            
            with open(logo_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # Remove first line if it exists
            if lines:
                lines = lines[1:]
            
            # Replace line 8 (now line 7) with custom email and info
            custom_line = f"  {self.new_email}     https://github.com/your-repo/CustomMikroTikPatch\n"
            
            if len(lines) >= 7:
                lines[6] = custom_line  # 0-indexed, so line 7
            else:
                # Pad with empty lines if needed
                while len(lines) < 7:
                    lines.append("\n")
                lines.append(custom_line)
            
            # Write back
            with open(logo_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            print(f"Updated logo file: {logo_path}")
            return True
            
        except Exception as e:
            print(f"Error updating logo file {logo_path}: {e}")
            return False
    
    def scan_directory(self, directory, extensions=None):
        """Scan directory for files containing the email"""
        if extensions is None:
            extensions = ['.txt', '.cfg', '.conf', '.xml', '.json', '.sh', '.py', '.c', '.h']
        
        found_files = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                
                # Check by extension
                if extensions and not any(file.lower().endswith(ext) for ext in extensions):
                    continue
                
                try:
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        if self.old_email_bytes in content:
                            found_files.append(file_path)
                except:
                    continue
        
        return found_files
    
    def scan_binary_files(self, directory):
        """Scan binary files for email addresses"""
        binary_extensions = ['.bin', '.elf', '.so', '.npk', '.ko', '.img', '.rgz']
        found_files = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                
                # Check binary files
                if any(file.lower().endswith(ext) for ext in binary_extensions):
                    try:
                        with open(file_path, 'rb') as f:
                            content = f.read()
                            if self.old_email_bytes in content:
                                found_files.append(file_path)
                    except:
                        continue
        
        return found_files
    
    def replace_in_directory(self, directory, include_binaries=True):
        """Replace email in all files in directory"""
        print(f"Scanning directory: {directory}")
        print(f"Replacing: {self.old_email} -> {self.new_email}")
        
        # Scan text files
        text_files = self.scan_directory(directory)
        print(f"Found {len(text_files)} text files with email")
        
        # Process text files
        for file_path in text_files:
            if self.replace_in_file(file_path):
                print(f"  ✓ {file_path}")
            self.files_processed += 1
        
        # Scan and process binary files
        if include_binaries:
            binary_files = self.scan_binary_files(directory)
            print(f"Found {len(binary_files)} binary files with email")
            
            for file_path in binary_files:
                if self.replace_in_binary(file_path):
                    print(f"  ✓ {file_path}")
                self.files_processed += 1
        
        # Special handling for logo file
        logo_paths = [
            os.path.join(directory, "nova/lib/console/logo.txt"),
            os.path.join(directory, "lib/console/logo.txt"),
            os.path.join(directory, "console/logo.txt")
        ]
        
        for logo_path in logo_paths:
            if self.replace_in_logo_file(logo_path):
                break
        
        print(f"\nReplacement complete:")
        print(f"  Files processed: {self.files_processed}")
        print(f"  Replacements made: {self.replacements_made}")
    
    def create_replacement_report(self, directory):
        """Create a report of files that would be modified"""
        text_files = self.scan_directory(directory)
        binary_files = self.scan_binary_files(directory)
        
        report = {
            'old_email': self.old_email,
            'new_email': self.new_email,
            'text_files': text_files,
            'binary_files': binary_files,
            'total_files': len(text_files) + len(binary_files)
        }
        
        return report

def main():
    """Main email replacement function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RouterOS Email Replacer')
    parser.add_argument('directory', help='Directory to process')
    parser.add_argument('--old-email', default='<EMAIL>', 
                       help='Old email to replace')
    parser.add_argument('--new-email', default='<EMAIL>',
                       help='New email address')
    parser.add_argument('--report-only', action='store_true',
                       help='Generate report without making changes')
    parser.add_argument('--no-binaries', action='store_true',
                       help='Skip binary files')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.directory):
        print(f"Error: Directory {args.directory} does not exist")
        sys.exit(1)
    
    replacer = EmailReplacer(args.old_email, args.new_email)
    
    if args.report_only:
        print("Generating replacement report...")
        report = replacer.create_replacement_report(args.directory)
        
        print(f"\nEmail Replacement Report")
        print(f"========================")
        print(f"Directory: {args.directory}")
        print(f"Old email: {report['old_email']}")
        print(f"New email: {report['new_email']}")
        print(f"Text files to modify: {len(report['text_files'])}")
        print(f"Binary files to modify: {len(report['binary_files'])}")
        print(f"Total files: {report['total_files']}")
        
        if report['text_files']:
            print(f"\nText files:")
            for file_path in report['text_files']:
                print(f"  {file_path}")
        
        if report['binary_files']:
            print(f"\nBinary files:")
            for file_path in report['binary_files']:
                print(f"  {file_path}")
    
    else:
        print("Starting email replacement...")
        replacer.replace_in_directory(args.directory, not args.no_binaries)
        print("Email replacement completed!")

if __name__ == '__main__':
    main()
