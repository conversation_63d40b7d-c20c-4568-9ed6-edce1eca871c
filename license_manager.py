#!/usr/bin/env python3
"""
Advanced MikroTik License Manager
Comprehensive license management system with database, validation, and server simulation.
"""

import os
import sys
import json
import sqlite3
import datetime
from pathlib import Path
from mikrotik_keygen import MikroTikLicenseGenerator
from mikro import mikro_softwareid_encode, mikro_softwareid_decode

class LicenseDatabase:
    """SQLite database for license management"""
    
    def __init__(self, db_path="licenses.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the license database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                software_id TEXT NOT NULL,
                version INTEGER NOT NULL,
                level INTEGER NOT NULL,
                license_key TEXT NOT NULL,
                private_key TEXT NOT NULL,
                public_key TEXT NOT NULL,
                nonce TEXT NOT NULL,
                signature TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                customer_email TEXT,
                notes TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS key_pairs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                private_key TEXT NOT NULL,
                public_key TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_default BOOLEAN DEFAULT 0
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_key_pair(self, name, private_key, public_key, is_default=False):
        """Add a key pair to the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if is_default:
            # Remove default flag from other keys
            cursor.execute('UPDATE key_pairs SET is_default = 0')
        
        cursor.execute('''
            INSERT INTO key_pairs (name, private_key, public_key, is_default)
            VALUES (?, ?, ?, ?)
        ''', (name, private_key, public_key, is_default))
        
        conn.commit()
        conn.close()
    
    def get_default_key_pair(self):
        """Get the default key pair"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT private_key, public_key FROM key_pairs WHERE is_default = 1')
        result = cursor.fetchone()
        
        conn.close()
        return result if result else (None, None)
    
    def add_license(self, software_id, version, level, license_key, private_key, 
                   public_key, nonce, signature, customer_email=None, expires_days=365):
        """Add a license to the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        expires_at = datetime.datetime.now() + datetime.timedelta(days=expires_days)
        
        cursor.execute('''
            INSERT INTO licenses (software_id, version, level, license_key, private_key,
                                public_key, nonce, signature, customer_email, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (software_id, version, level, license_key, private_key, public_key,
              nonce, signature, customer_email, expires_at))
        
        license_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return license_id
    
    def get_licenses(self, active_only=True):
        """Get all licenses from database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = 'SELECT * FROM licenses'
        if active_only:
            query += ' WHERE is_active = 1'
        query += ' ORDER BY created_at DESC'
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        conn.close()
        return results

class LicenseManager:
    """Advanced license management system"""
    
    def __init__(self):
        self.db = LicenseDatabase()
        self.generator = None
        self.load_default_keys()
    
    def load_default_keys(self):
        """Load default key pair"""
        private_key, public_key = self.db.get_default_key_pair()
        if private_key:
            self.generator = MikroTikLicenseGenerator(private_key)
            print(f"Loaded default key pair: {private_key[:16]}...")
        else:
            print("No default key pair found. Generating new keys...")
            self.generate_and_save_keys("default", is_default=True)
    
    def generate_and_save_keys(self, name, is_default=False):
        """Generate and save a new key pair"""
        from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
        from cryptography.hazmat.primitives import serialization
        
        # Generate key pair
        private_key_obj = Ed25519PrivateKey.generate()
        public_key_obj = private_key_obj.public_key()
        
        private_bytes = private_key_obj.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_bytes = public_key_obj.public_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PublicFormat.Raw
        )
        
        private_key_hex = private_bytes.hex()
        public_key_hex = public_bytes.hex()
        
        # Save to database
        self.db.add_key_pair(name, private_key_hex, public_key_hex, is_default)
        
        if is_default:
            self.generator = MikroTikLicenseGenerator(private_key_hex)
        
        print(f"Generated key pair '{name}':")
        print(f"  Private: {private_key_hex[:16]}...")
        print(f"  Public:  {public_key_hex[:16]}...")
        
        return private_key_hex, public_key_hex
    
    def create_license(self, software_id=None, version=7, level=4, customer_email=None, expires_days=365):
        """Create a new license"""
        if not self.generator:
            raise Exception("No key pair loaded")
        
        # Generate software ID if not provided
        if not software_id:
            import random
            software_id = mikro_softwareid_encode(random.randint(1000, 999999))
        
        # Generate license
        license_key = self.generator.create_license_key(software_id, version, level)
        
        # Extract nonce and signature from license
        lines = license_key.split('\n')
        nonce = ""
        signature = ""
        
        for line in lines:
            if line.startswith("Nonce:"):
                nonce = line.split(": ")[1]
            elif line.startswith("Signature:"):
                signature = line.split(": ")[1]
        
        # Get current keys
        private_key, public_key = self.db.get_default_key_pair()
        
        # Save to database
        license_id = self.db.add_license(
            software_id, version, level, license_key, private_key, public_key,
            nonce, signature, customer_email, expires_days
        )
        
        print(f"Created license #{license_id} for {software_id}")
        return license_id, license_key
    
    def list_licenses(self):
        """List all licenses"""
        licenses = self.db.get_licenses()
        
        print("\nLicense Database:")
        print("=" * 80)
        print(f"{'ID':<4} {'Software ID':<12} {'Ver':<3} {'Lvl':<3} {'Customer':<25} {'Created':<19}")
        print("-" * 80)
        
        for license_data in licenses:
            license_id = license_data[0]
            software_id = license_data[1]
            version = license_data[2]
            level = license_data[3]
            created_at = license_data[9]
            customer_email = license_data[12] or "N/A"
            
            print(f"{license_id:<4} {software_id:<12} {version:<3} {level:<3} {customer_email:<25} {created_at:<19}")
    
    def export_license(self, license_id, filename=None):
        """Export a license to file"""
        conn = sqlite3.connect(self.db.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT license_key, software_id FROM licenses WHERE id = ?', (license_id,))
        result = cursor.fetchone()
        
        if not result:
            print(f"License #{license_id} not found")
            return False
        
        license_key, software_id = result
        
        if not filename:
            filename = f"license_{software_id}_{license_id}.key"
        
        with open(filename, 'w') as f:
            f.write(license_key)
        
        print(f"License exported to {filename}")
        conn.close()
        return True
    
    def validate_license(self, license_key):
        """Validate a license key"""
        if not self.generator:
            return False
        
        private_key, public_key = self.db.get_default_key_pair()
        if not public_key:
            return False
        
        return self.generator.verify_license(license_key, public_key)

def main():
    """Main license manager interface"""
    manager = LicenseManager()
    
    print("Advanced MikroTik License Manager")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Create license")
        print("2. List licenses")
        print("3. Export license")
        print("4. Generate key pair")
        print("5. Validate license")
        print("6. Bulk create licenses")
        print("7. Exit")
        
        choice = input("\nEnter choice (1-7): ").strip()
        
        if choice == '1':
            software_id = input("Software ID (leave empty for random): ").strip() or None
            version = int(input("Version (default 7): ").strip() or "7")
            level = int(input("Level (default 4): ").strip() or "4")
            customer_email = input("Customer email (optional): ").strip() or None
            
            license_id, license_key = manager.create_license(software_id, version, level, customer_email)
            print(f"\nGenerated License #{license_id}:")
            print("-" * 50)
            print(license_key)
            
        elif choice == '2':
            manager.list_licenses()
            
        elif choice == '3':
            license_id = int(input("License ID to export: "))
            filename = input("Filename (optional): ").strip() or None
            manager.export_license(license_id, filename)
            
        elif choice == '4':
            name = input("Key pair name: ").strip()
            is_default = input("Set as default? (y/n): ").strip().lower() == 'y'
            manager.generate_and_save_keys(name, is_default)
            
        elif choice == '5':
            filename = input("License file path: ").strip()
            try:
                with open(filename, 'r') as f:
                    license_content = f.read()
                is_valid = manager.validate_license(license_content)
                print(f"License is {'VALID' if is_valid else 'INVALID'}")
            except FileNotFoundError:
                print("File not found")
            
        elif choice == '6':
            count = int(input("Number of licenses to create: "))
            version = int(input("Version (default 7): ").strip() or "7")
            level = int(input("Level (default 4): ").strip() or "4")
            
            print(f"Creating {count} licenses...")
            for i in range(count):
                license_id, _ = manager.create_license(version=version, level=level)
                print(f"Created license #{license_id}")
            
        elif choice == '7':
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice")

if __name__ == '__main__':
    main()
