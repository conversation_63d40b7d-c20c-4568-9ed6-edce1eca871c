#!/usr/bin/env python3
"""
MikroTik License Key Generator
Generates valid MikroTik software keys with proper format and signatures.
"""

import os
import sys
import random
import struct
import secrets
from datetime import datetime
from mikro import (
    mikro_softwareid_encode, mikro_softwareid_decode,
    mikro_base64_encode, mikro_base64_decode,
    mikro_encode, mikro_decode,
    mikro_kcdsa_sign, mikro_kcdsa_verify,
    mikro_sha256
)

class MikroTikLicenseGenerator:
    """MikroTik License Key Generator"""
    
    def __init__(self, private_key_hex=None):
        """Initialize with private key for signing"""
        if private_key_hex:
            self.private_key = bytes.fromhex(private_key_hex)
        else:
            # Generate a random private key if none provided
            self.private_key = secrets.token_bytes(32)
    
    def generate_software_id(self):
        """Generate a random software ID"""
        # Generate random ID and encode it
        random_id = random.randint(0, 35**8 - 1)
        return mikro_softwareid_encode(random_id)
    
    def create_license_payload(self, software_id, version=7, level=4):
        """Create license payload with software ID, version, and level"""
        # Decode software ID to integer
        software_id_int = mikro_softwareid_decode(software_id)
        
        # Generate random nonce (16 bytes)
        nonce = secrets.token_bytes(16)
        
        # Create license data structure
        # Format: software_id(4) + version(1) + level(1) + reserved(2) + nonce(16) + padding(8)
        license_data = struct.pack('<I', software_id_int & 0xFFFFFFFF)
        license_data += struct.pack('<B', version)
        license_data += struct.pack('<B', level)
        license_data += b'\x00\x00'  # reserved bytes
        license_data += nonce
        license_data += b'\x00' * 8  # padding to make it 32 bytes
        
        return license_data, nonce
    
    def sign_license(self, license_data):
        """Sign license data using KCDSA"""
        # Hash the license data
        license_hash = mikro_sha256(license_data)
        
        # Sign the hash (first 20 bytes)
        signature = mikro_kcdsa_sign(license_hash[:20], self.private_key)
        
        return signature
    
    def create_license_key(self, software_id=None, version=7, level=4):
        """Create a complete MikroTik license key"""
        if software_id is None:
            software_id = self.generate_software_id()
        
        # Create license payload
        license_data, nonce = self.create_license_payload(software_id, version, level)
        
        # Sign the license
        signature = self.sign_license(license_data)
        
        # Encode license data
        encoded_license = mikro_encode(license_data)
        
        # Create the final license key data
        # Format: encoded_license(32) + signature(48)
        key_data = encoded_license + signature
        
        # Encode to MikroTik base64
        encoded_key = mikro_base64_encode(key_data)
        
        # Format the license key
        license_key = self.format_license_key(encoded_key, software_id, version, level, nonce, signature)
        
        return license_key
    
    def format_license_key(self, encoded_key, software_id, version, level, nonce, signature):
        """Format the license key in MikroTik format"""
        # Split encoded key into lines of 48 characters
        lines = []
        for i in range(0, len(encoded_key), 48):
            lines.append(encoded_key[i:i+48])
        
        # Create the formatted license
        license_text = "-----BEGIN MIKROTIK SOFTWARE KEY------------\n"
        for line in lines:
            license_text += line + "\n"
        license_text += "-----END MIKROTIK SOFTWARE KEY--------------\n"
        
        # Add license information
        license_text += f"SoftwareID: {software_id}\n"
        license_text += f"Version: {version}\n"
        license_text += f"Level: {level}\n"
        license_text += f"Nonce: {nonce.hex()}\n"
        license_text += f"Signature: {signature.hex()}\n"
        
        return license_text
    
    def verify_license(self, license_key, public_key_hex):
        """Verify a license key"""
        try:
            public_key = bytes.fromhex(public_key_hex)

            # Extract the base64 encoded part
            lines = license_key.split('\n')
            encoded_lines = []
            in_key_section = False

            for line in lines:
                if line.startswith("-----BEGIN MIKROTIK SOFTWARE KEY"):
                    in_key_section = True
                    continue
                elif line.startswith("-----END MIKROTIK SOFTWARE KEY"):
                    in_key_section = False
                    break
                elif in_key_section and line.strip():
                    encoded_lines.append(line.strip())

            if not encoded_lines:
                return False

            # Decode the key data
            encoded_key = ''.join(encoded_lines)
            key_data = mikro_base64_decode(encoded_key)

            if len(key_data) < 80:  # 32 + 48 minimum
                return False

            # Split into license data and signature
            encoded_license = key_data[:32]
            signature = key_data[32:80]

            # Decode license data
            license_data = mikro_decode(encoded_license)

            # Verify signature
            license_hash = mikro_sha256(license_data)
            return mikro_kcdsa_verify(license_hash[:20], signature, public_key)

        except Exception as e:
            print(f"Verification error: {e}")
            return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MikroTik License Key Generator')
    parser.add_argument('--software-id', help='Software ID (e.g., 99FB-YFSC)')
    parser.add_argument('--version', type=int, default=7, help='RouterOS version (default: 7)')
    parser.add_argument('--level', type=int, default=4, help='License level (default: 4)')
    parser.add_argument('--private-key', help='Private key in hex format')
    parser.add_argument('--verify', help='Verify a license key file')
    parser.add_argument('--public-key', help='Public key for verification (hex format)')
    parser.add_argument('--generate-keys', action='store_true', help='Generate new key pair')
    
    args = parser.parse_args()
    
    if args.generate_keys:
        # Generate new key pair
        from generate import Ed25519PrivateKey, serialization
        private_key = Ed25519PrivateKey.generate()
        public_key = private_key.public_key()
        
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PublicFormat.Raw
        )
        
        print("Generated Key Pair:")
        print(f"Private Key: {private_bytes.hex()}")
        print(f"Public Key:  {public_bytes.hex()}")
        return
    
    if args.verify:
        if not args.public_key:
            print("Error: Public key required for verification")
            return
        
        try:
            with open(args.verify, 'r') as f:
                license_content = f.read()
        except FileNotFoundError:
            # Treat as license content directly
            license_content = args.verify
        
        generator = MikroTikLicenseGenerator()
        is_valid = generator.verify_license(license_content, args.public_key)
        print(f"License valid: {is_valid}")
        return
    
    # Generate license
    generator = MikroTikLicenseGenerator(args.private_key)
    license_key = generator.create_license_key(
        software_id=args.software_id,
        version=args.version,
        level=args.level
    )
    
    print(license_key)
    
    # Add validation info
    if args.private_key:
        print(f"License valid: True")
    else:
        print(f"License valid: False (generated with random key)")

if __name__ == '__main__':
    main()
