#!/usr/bin/env python3
"""
Create Valid MikroTik Licenses
Generates properly validated licenses using your custom keys.
"""

import os
import re
from mikrotik_keygen import MikroTikLicenseGenerator
from mikro import mikro_softwareid_encode

def load_custom_keys():
    """Load custom keys from environment file"""
    try:
        with open('env.ps1', 'r') as f:
            content = f.read()
        
        # Extract private key
        private_key_match = re.search(r'CUSTOM_LICENSE_PRIVATE_KEY = "([^"]+)"', content)
        public_key_match = re.search(r'CUSTOM_LICENSE_PUBLIC_KEY = "([^"]+)"', content)
        
        if private_key_match and public_key_match:
            return private_key_match.group(1), public_key_match.group(1)
        else:
            print("Could not find keys in env.ps1")
            return None, None
            
    except FileNotFoundError:
        print("env.ps1 not found. Run setup_simple.ps1 first.")
        return None, None

def create_valid_license_set():
    """Create a set of valid licenses"""
    
    print("🔑 CREATING VALID MIKROTIK LICENSES")
    print("=" * 50)
    
    # Load custom keys
    private_key, public_key = load_custom_keys()
    
    if not private_key:
        print("❌ No custom keys found. Generating new ones...")
        generator = MikroTikLicenseGenerator()
        
        # Generate new keys
        from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
        from cryptography.hazmat.primitives import serialization
        
        key_obj = Ed25519PrivateKey.generate()
        private_bytes = key_obj.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_bytes = key_obj.public_key().public_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PublicFormat.Raw
        )
        
        private_key = private_bytes.hex()
        public_key = public_bytes.hex()
        
        print(f"✅ Generated new keys:")
        print(f"   Private: {private_key[:16]}...")
        print(f"   Public:  {public_key[:16]}...")
    else:
        print(f"✅ Using existing custom keys:")
        print(f"   Private: {private_key[:16]}...")
        print(f"   Public:  {public_key[:16]}...")
    
    # Create generator with custom key
    generator = MikroTikLicenseGenerator(private_key)
    
    # Define license configurations
    license_configs = [
        {
            'name': 'Standard RouterOS 7 License',
            'software_id': None,  # Will generate valid ID
            'version': 7,
            'level': 4,
            'description': 'Standard features for RouterOS 7'
        },
        {
            'name': 'Unlimited RouterOS 7 License', 
            'software_id': None,  # Will generate valid ID
            'version': 7,
            'level': 6,
            'description': 'All features unlocked for RouterOS 7'
        },
        {
            'name': 'RouterOS 6 Legacy License',
            'software_id': None,  # Will generate valid ID
            'version': 6,
            'level': 6,
            'description': 'Legacy RouterOS 6 with all features'
        },
        {
            'name': 'Custom Software ID License',
            'software_id': 'CUST-OM01',  # Try custom ID
            'version': 7,
            'level': 4,
            'description': 'License with custom software ID'
        }
    ]
    
    print(f"\n📋 GENERATING {len(license_configs)} VALID LICENSES")
    print("=" * 50)
    
    valid_licenses = []
    
    for i, config in enumerate(license_configs, 1):
        print(f"\n{i}. {config['name']}")
        print("-" * 40)
        
        # Generate or use software ID
        if config['software_id']:
            try:
                # Try to use the provided software ID
                software_id = config['software_id']
                license_key = generator.create_license_key(
                    software_id, config['version'], config['level']
                )
            except Exception as e:
                print(f"   ⚠️  Custom ID failed: {e}")
                print(f"   🔄 Generating valid software ID...")
                software_id = mikro_softwareid_encode(1000 + i)
                license_key = generator.create_license_key(
                    software_id, config['version'], config['level']
                )
        else:
            # Generate valid software ID
            software_id = mikro_softwareid_encode(1000 + i)
            license_key = generator.create_license_key(
                software_id, config['version'], config['level']
            )
        
        print(f"   Software ID: {software_id}")
        print(f"   Version: {config['version']}")
        print(f"   Level: {config['level']}")
        print(f"   Description: {config['description']}")
        print(f"   ✅ License generated successfully!")
        
        # Save license to file
        filename = f"license_{i}_{software_id.replace('-', '_')}.key"
        with open(filename, 'w') as f:
            f.write(license_key)
        
        print(f"   💾 Saved to: {filename}")
        
        valid_licenses.append({
            'config': config,
            'software_id': software_id,
            'license_key': license_key,
            'filename': filename
        })
    
    # Display all generated licenses
    print(f"\n🎉 ALL LICENSES GENERATED SUCCESSFULLY!")
    print("=" * 50)
    
    for i, license_info in enumerate(valid_licenses, 1):
        print(f"\n📄 License {i}: {license_info['config']['name']}")
        print("-" * 50)
        print(license_info['license_key'])
    
    # Create summary report
    print(f"\n📊 SUMMARY REPORT")
    print("=" * 50)
    print(f"Total licenses generated: {len(valid_licenses)}")
    print(f"Private key used: {private_key[:16]}...")
    print(f"Public key: {public_key[:16]}...")
    print("\nGenerated files:")
    for license_info in valid_licenses:
        print(f"  • {license_info['filename']}")
    
    print(f"\n🔐 KEY INFORMATION")
    print("=" * 50)
    print("These licenses are signed with your custom private key.")
    print("For them to be valid in RouterOS, you need to:")
    print("1. Build custom RouterOS with your public key embedded")
    print("2. Replace MikroTik's public key with yours in the system")
    print("3. Use the build scripts provided to create custom RouterOS")
    
    print(f"\n🚀 NEXT STEPS")
    print("=" * 50)
    print("1. Test licenses in virtual machine with custom RouterOS")
    print("2. Use build_orchestrator.py to create custom RouterOS ISO")
    print("3. Deploy custom RouterOS with your embedded public key")
    print("4. Apply these licenses - they will validate as 'License valid: True'")
    
    return valid_licenses

def verify_generated_licenses():
    """Verify the generated licenses"""
    print(f"\n🔍 VERIFYING GENERATED LICENSES")
    print("=" * 50)
    
    # Load keys
    private_key, public_key = load_custom_keys()
    if not private_key:
        print("❌ No keys available for verification")
        return
    
    # Find license files
    import glob
    license_files = glob.glob("license_*.key")
    
    if not license_files:
        print("❌ No license files found")
        return
    
    generator = MikroTikLicenseGenerator(private_key)
    
    for license_file in license_files:
        print(f"\n📄 Verifying: {license_file}")
        
        try:
            with open(license_file, 'r') as f:
                license_content = f.read()
            
            # Verify with our public key
            is_valid = generator.verify_license(license_content, public_key)
            
            if is_valid:
                print(f"   ✅ VALID - License signature verified")
            else:
                print(f"   ❌ INVALID - Signature verification failed")
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")

def main():
    """Main function"""
    print("🎯 MIKROTIK VALID LICENSE GENERATOR")
    print("=" * 60)
    print("This tool creates properly validated MikroTik licenses")
    print("using your custom cryptographic keys.")
    print("=" * 60)
    
    # Create valid licenses
    licenses = create_valid_license_set()
    
    # Verify them
    verify_generated_licenses()
    
    print(f"\n✨ PROCESS COMPLETE!")
    print("=" * 60)
    print("Your valid MikroTik licenses are ready for use!")

if __name__ == '__main__':
    main()
